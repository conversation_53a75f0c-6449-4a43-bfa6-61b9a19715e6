# InsightStream

> **Unlock the Knowledge Within Video**

InsightStream is a revolutionary AI-powered video interaction platform that transforms passive video consumption into an active, intelligent learning experience. Using advanced multi-agent AI architecture and Retrieval Augmented Generation (RAG), InsightStream enables users to engage with video content like never before.

## 🌟 Key Features

### 🤖 **Multi-Agent AI System**

- **6 Specialized AI Agents** for optimal performance
- **Contextual Q&A** with RAG-powered understanding
- **Intelligent Note Enhancement** with AI assistance
- **Voice Interaction** with natural speech commands

### 🎥 **Universal Video Support**

- **YouTube, TikTok, LinkedIn, Facebook** and more
- **Real-time Transcript Synchronization**
- **Interactive Video Navigation**
- **Cross-platform Unified Experience**

### 📝 **Advanced Note-Taking**

- **AI-Enhanced Multimedia Notes**
- **Automatic Timestamping**
- **Voice-to-Text Note Creation**
- **Cross-video Knowledge Connections**

### 🎯 **Intelligent Features**

- **Temporal Context Understanding**
- **Predictive Question Generation**
- **Content Discovery & Recommendations**
- **Comprehensive Session Management**

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** for FastAPI backend
- **uv** - Modern Python package manager ([install guide](https://docs.astral.sh/uv/))
- **Node.js 18+** for React frontend
- **Neon PostgreSQL** account (get free database at neon.tech)
- **Redis** (local installation or cloud service)

### One-Command Setup

```bash
# Clone the repository
git clone <repository-url>
cd insightstream

# Quick setup
make quick-start
```

This will:

1. Set up Python virtual environment and install dependencies
2. Set up Node.js environment and install packages
3. Verify database connection to Neon PostgreSQL
4. Provide instructions for starting development servers

### Access the Application

- **Frontend**: <http://localhost:3000>
- **Backend API**: <http://localhost:8000>
- **API Documentation**: <http://localhost:8000/docs>

## 🏗️ Architecture

### Technology Stack

**Frontend**

- React 18+ with TypeScript 5+
- Vite for fast development and building
- Zustand for state management
- TanStack Query for server state
- Tailwind CSS for styling

**Backend**

- FastAPI with Python 3.11+
- uv for fast Python package management
- yt-dlp for universal video downloading (1000+ sites)
- youtube-transcript-api for reliable transcript extraction
- SQLModel with Neon PostgreSQL and asyncpg
- Alembic for database migrations
- Celery for background tasks
- Ruff for linting and formatting
- MyPy for type checking
- Logfire for logging and monitoring

**AI & Processing**

- Agno.com multi-agent AI orchestration
- Groq API for fast, free LLM inference
- Pydantic-AI for structured I/O
- Sentence Transformers for free embeddings
- Qdrant vector database for RAG
- Browser Web Speech API for free voice interaction
- OpenAI Whisper for transcription (minimal cost)
- Optional ElevenLabs for premium TTS

### Multi-Agent AI Architecture

```text
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Video Analysis  │    │    Q&A Agent    │    │ Note Assistant  │
│     Agent       │    │                 │    │     Agent       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ AI Orchestrator │
                    └─────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│ Transcript      │    │ Speech Processor│    │ Content Discovery│
│    Agent        │    │     Agent       │    │     Agent       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 📚 Documentation

### Core Documentation

- [**📚 Documentation Hub**](docs/README.md) - Complete documentation index and navigation
- [**🔧 Backend Guide**](backend/README.md) - FastAPI backend setup and architecture
- [**🎨 Frontend Guide**](frontend/README.md) - React frontend with voice features
- [**Project Overview**](docs/INSIGHTSTREAM_OVERVIEW.md) - Comprehensive app explanation
- [**Development Setup**](docs/DEVELOPMENT_SETUP.md) - Complete setup guide
- [**Speech Implementation**](docs/SPEECH_IMPLEMENTATION_GUIDE.md) - Free voice features guide
- [**API Specifications**](docs/API_SPECIFICATIONS.md) - Detailed API documentation

### Technical Documentation

- [**System Architecture**](docs/SYSTEM_DIAGRAMS.md) - Architecture diagrams and flows
- [**Implementation Roadmap**](docs/ENHANCED_IMPLEMENTATION_ROADMAP.md) - 24-week development plan
- [**Testing Strategy**](docs/COMPREHENSIVE_TESTING_STRATEGY.md) - Quality assurance approach

### Advanced Features

- [**Innovation Features**](docs/INNOVATION_FEATURES.md) - Unique capabilities and differentiation
- [**Production Deployment**](docs/PRODUCTION_DEPLOYMENT_GUIDE.md) - Enterprise deployment guide

## 🛠️ Development

### Common Commands

```bash
# Development
make dev                 # Start development environment
make dev-backend        # Start backend only
make dev-frontend       # Start frontend only

# Testing
make test               # Run all tests
make test-backend       # Backend tests only
make test-frontend      # Frontend tests only
make coverage           # Generate coverage reports

# Code Quality
make lint               # Run linting
make format             # Format code
make dev-workflow       # Complete dev workflow (format + lint + test)

# Database
make db-migrate         # Run migrations
make db-seed           # Seed test data
make db-reset          # Reset database
make db-connect        # Test Neon PostgreSQL connection
```

### Project Structure

```text
insightstream/
├── backend/           # FastAPI backend with multi-agent AI
├── frontend/          # React frontend with TypeScript
├── docs/             # Comprehensive documentation
├── scripts/          # Development and deployment scripts
├── .github/          # CI/CD workflows
├── Makefile          # Development commands
└── .env.example      # Environment configuration template
```

## 🧪 Testing

InsightStream maintains >95% test coverage with comprehensive testing:

```bash
# Run all tests
make test

# Backend testing
cd backend
pytest --cov=app --cov-report=html

# Frontend testing
cd frontend
npm test
npm run e2e

# Load testing
cd backend
locust -f tests/load_test.py
```

## 🚀 Deployment

### Development

```bash
# Start FastAPI backend
make dev-backend

# Start React frontend (in another terminal)
make dev-frontend

# Or start both
make dev-full
```

### Production

```bash
# Build production artifacts
make build

# Deploy to production
make deploy
```

### Environment Configuration

Copy `.env.example` to `.env` and configure:

```bash
# Neon PostgreSQL Database (get from neon.tech)
DATABASE_URL=postgresql+asyncpg://username:<EMAIL>/insightstream_db?sslmode=require

# AI Services
AGNO_API_KEY=your-agno-api-key
GROQ_API_KEY=your-groq-api-key  # Free tier available

# Voice (Browser-based - Free)
VOICE_ENABLED=true
VOICE_PROVIDER=browser  # No API key required

# LLM Configuration
PRIMARY_LLM_PROVIDER=groq
GROQ_MODEL=llama3-8b-8192

# Knowledge Graph
KNOWLEDGE_GRAPH_ENABLED=true
NEO4J_URI=bolt://localhost:7687  # Optional: for advanced graph features

# Monitoring
LOGFIRE_TOKEN=your-logfire-token
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Follow the development workflow**: `make dev-workflow`
4. **Commit your changes**: `git commit -m 'Add amazing feature'`
5. **Push to the branch**: `git push origin feature/amazing-feature`
6. **Open a Pull Request**

### Development Guidelines

- **Code Quality**: Maintain >90% test coverage
- **Documentation**: Update docs for new features
- **Type Safety**: Use TypeScript and Python type hints
- **Performance**: Ensure <2s response times for AI interactions
- **Security**: Follow security best practices

## 📊 Performance Metrics

### Target Performance

- **AI Response Time**: <2 seconds for 95% of queries
- **Video Processing**: <2 minutes for analysis completion
- **Transcript Accuracy**: >95% for clear audio
- **Uptime**: 99.9% availability
- **Concurrent Users**: Support for 10,000+ users

### Monitoring

InsightStream uses comprehensive monitoring:

- **Logfire** for application logging and monitoring
- **Prometheus** for metrics collection
- **Grafana** for visualization
- **Sentry** for error tracking

## 🔒 Security

- **JWT Authentication** with refresh tokens
- **Rate Limiting** on all API endpoints
- **Data Encryption** at rest and in transit
- **GDPR Compliance** with privacy controls
- **Regular Security Audits**

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **Agno.com** for advanced AI agent capabilities
- **OpenAI** for Whisper transcription technology
- **ElevenLabs** for natural text-to-speech
- **FastAPI** for the excellent Python web framework
- **React** team for the powerful frontend library

## 📞 Support

- **Documentation**: [docs/](docs/)
- **Issues**: [GitHub Issues](https://github.com/your-org/insightstream/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/insightstream/discussions)

---

**InsightStream** - Transforming how the world learns from video content, one insight at a time.

*Unlock the Knowledge Within Video* 🎥✨
