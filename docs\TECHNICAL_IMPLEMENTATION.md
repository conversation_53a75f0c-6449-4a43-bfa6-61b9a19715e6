# Technical Implementation Guide

## Project Structure

```plaintext
insight_stream/
├── backend/
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py                 # FastAPI application entry point
│   │   ├── core/
│   │   │   ├── config.py          # Configuration management
│   │   │   ├── security.py        # Authentication & authorization
│   │   │   └── database.py        # Database connection & session management
│   │   ├── models/
│   │   │   ├── __init__.py
│   │   │   ├── user.py            # User-related SQLModel models
│   │   │   ├── video.py           # Video & transcript models
│   │   │   └── interaction.py     # AI interaction & notes models
│   │   ├── api/
│   │   │   ├── __init__.py
│   │   │   ├── deps.py            # Dependency injection
│   │   │   └── v1/
│   │   │       ├── __init__.py
│   │   │       ├── auth.py        # Authentication endpoints
│   │   │       ├── videos.py      # Video processing endpoints
│   │   │       ├── ai.py          # AI interaction endpoints
│   │   │       └── notes.py       # Note-taking endpoints
│   │   ├── services/
│   │   │   ├── __init__.py
│   │   │   ├── video_processor.py # Video extraction & processing
│   │   │   ├── transcript_service.py # Transcript extraction
│   │   │   ├── ai_service.py      # Agno AI agent integration
│   │   │   └── search_service.py  # Vector search & semantic queries
│   │   ├── schemas/
│   │   │   ├── __init__.py
│   │   │   ├── video.py           # Pydantic request/response models
│   │   │   ├── ai.py              # AI interaction schemas
│   │   │   └── user.py            # User-related schemas
│   │   └── utils/
│   │       ├── __init__.py
│   │       ├── exceptions.py      # Custom exception classes
│   │       └── helpers.py         # Utility functions
│   ├── alembic/                   # Database migrations
│   ├── tests/                     # Comprehensive test suite
│   ├── requirements.txt
│   └── Dockerfile
├── frontend/
│   ├── src/
│   │   ├── components/
│   │   │   ├── VideoPlayer/       # Custom video player component
│   │   │   ├── Transcript/        # Interactive transcript display
│   │   │   ├── AIChat/           # AI interaction interface
│   │   │   ├── Notes/            # Note-taking components
│   │   │   └── common/           # Reusable UI components
│   │   ├── hooks/                # Custom React hooks
│   │   ├── services/             # API client & data fetching
│   │   ├── stores/               # Zustand state management
│   │   ├── types/                # TypeScript type definitions
│   │   └── utils/                # Frontend utility functions
│   ├── public/
│   ├── package.json
│   ├── vite.config.ts
│   └── Dockerfile
├── docker-compose.yml             # Development environment
├── docker-compose.prod.yml        # Production environment
└── README.md
```

## Core Implementation Details

### 1. Database Models (SQLModel)

```python
# backend/app/models/video.py
from sqlmodel import SQLModel, Field, Relationship
from typing import Optional, List
from datetime import datetime
from enum import Enum

class PlatformType(str, Enum):
    YOUTUBE = "youtube"
    TIKTOK = "tiktok"
    LINKEDIN = "linkedin"
    FACEBOOK = "facebook"
    INSTAGRAM = "instagram"

class Video(SQLModel, table=True):
    __tablename__ = "videos"

    id: Optional[int] = Field(default=None, primary_key=True)
    platform: PlatformType = Field(index=True)
    external_id: str = Field(unique=True, index=True, max_length=255)
    url: str = Field(max_length=2048)
    title: str = Field(max_length=500)
    description: Optional[str] = None
    duration: float  # Duration in seconds
    thumbnail_url: Optional[str] = Field(max_length=2048)
    channel_name: Optional[str] = Field(max_length=255)
    view_count: Optional[int] = None
    upload_date: Optional[datetime] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)
    updated_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    transcript: Optional["Transcript"] = Relationship(back_populates="video")
    user_notes: List["UserNote"] = Relationship(back_populates="video")
    ai_interactions: List["AIInteraction"] = Relationship(back_populates="video")

class Transcript(SQLModel, table=True):
    __tablename__ = "transcripts"

    id: Optional[int] = Field(default=None, primary_key=True)
    video_id: int = Field(foreign_key="videos.id", index=True)
    language: str = Field(default="en", max_length=10)
    source: str = Field(max_length=50)  # youtube-api, whisper, manual
    confidence_score: Optional[float] = None
    created_at: datetime = Field(default_factory=datetime.utcnow)

    # Relationships
    video: Video = Relationship(back_populates="transcript")
    segments: List["TranscriptSegment"] = Relationship(back_populates="transcript")

class TranscriptSegment(SQLModel, table=True):
    __tablename__ = "transcript_segments"

    id: Optional[int] = Field(default=None, primary_key=True)
    transcript_id: int = Field(foreign_key="transcripts.id", index=True)
    start_time: float = Field(index=True)  # Start time in seconds
    end_time: float = Field(index=True)    # End time in seconds
    text: str = Field(max_length=1000)
    confidence: Optional[float] = None
    speaker: Optional[str] = Field(max_length=100)

    # Relationships
    transcript: Transcript = Relationship(back_populates="segments")
```

### 2. AI Service Integration

```python
# backend/app/services/ai_service.py
from agno.agent import Agent
from agno.models.anthropic import Claude
from agno.tools.reasoning import ReasoningTools
from pydantic import BaseModel, Field
from typing import List, Optional
import asyncio

class VideoContext(BaseModel):
    video_title: str
    transcript_segments: List[str]
    time_range: Optional[tuple[float, float]] = None
    user_query: str

class AIResponse(BaseModel):
    answer: str = Field(description="The AI's response to the user's question")
    confidence: float = Field(description="Confidence score between 0 and 1")
    source_timestamps: List[float] = Field(description="Relevant timestamps in the video")
    related_topics: List[str] = Field(description="Related topics mentioned")
    follow_up_questions: List[str] = Field(description="Suggested follow-up questions")

class VideoAIService:
    def __init__(self):
        self.agent = Agent(
            model=Claude(id="claude-3-5-sonnet-latest"),
            tools=[ReasoningTools(add_instructions=True)],
            instructions=[
                "You are a video content analysis expert.",
                "Provide accurate, contextual responses based on video transcripts.",
                "Always include relevant timestamps when referencing specific content.",
                "Be concise but comprehensive in your analysis.",
                "If information is not available in the transcript, clearly state this."
            ],
            structured_outputs=True,
            response_model=AIResponse
        )

    async def analyze_video_content(self, context: VideoContext) -> AIResponse:
        """Analyze video content and provide contextual AI response"""

        # Prepare context for AI agent
        transcript_text = "\n".join([
            f"[{i*30:.1f}s] {segment}"
            for i, segment in enumerate(context.transcript_segments)
        ])

        if context.time_range:
            start, end = context.time_range
            prompt = f"""
            Video: {context.video_title}

            User Question: {context.user_query}
            Time Range: {start:.1f}s - {end:.1f}s

            Relevant Transcript:
            {transcript_text}

            Please analyze the content within the specified time range and provide a comprehensive answer.
            """
        else:
            prompt = f"""
            Video: {context.video_title}

            User Question: {context.user_query}

            Full Transcript:
            {transcript_text}

            Please analyze the entire video content and provide a comprehensive answer.
            """

        # Get AI response with structured output
        response = await asyncio.to_thread(
            self.agent.run,
            prompt,
            stream=False
        )

        return response

    async def generate_summary(self, video_title: str, transcript_segments: List[str]) -> str:
        """Generate a comprehensive summary of the video content"""

        transcript_text = "\n".join(transcript_segments)

        prompt = f"""
        Video: {video_title}

        Transcript:
        {transcript_text}

        Please provide a comprehensive summary of this video, including:
        1. Main topics discussed
        2. Key insights and takeaways
        3. Important timestamps for major topics
        4. Overall structure and flow
        """

        response = await asyncio.to_thread(
            self.agent.run,
            prompt,
            stream=False
        )

        return response.answer if hasattr(response, 'answer') else str(response)

    async def extract_key_topics(self, transcript_segments: List[str]) -> List[str]:
        """Extract key topics and themes from video content"""

        transcript_text = "\n".join(transcript_segments)

        prompt = f"""
        Transcript:
        {transcript_text}

        Extract the main topics, themes, and concepts discussed in this video.
        Return a list of key topics in order of importance.
        """

        response = await asyncio.to_thread(
            self.agent.run,
            prompt,
            stream=False
        )

        # Parse topics from response
        if hasattr(response, 'related_topics'):
            return response.related_topics
        else:
            # Fallback parsing if structured output fails
            topics = str(response).split('\n')
            return [topic.strip('- ').strip() for topic in topics if topic.strip()]
```

### 3. Video Processing Service

```python
# backend/app/services/video_processor.py
import yt_dlp
from youtube_transcript_api import YouTubeTranscriptApi
import asyncio
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse, parse_qs
import re

class VideoProcessingError(Exception):
    """Custom exception for video processing failures"""
    pass

class VideoProcessor:
    def __init__(self):
        self.ydl_opts = {
            'quiet': True,
            'no_warnings': True,
            'extract_flat': False,
            'writesubtitles': False,
            'writeautomaticsub': False,
        }

    async def extract_video_info(self, url: str) -> Dict:
        """Extract video metadata from URL using yt-dlp"""

        try:
            with yt_dlp.YoutubeDL(self.ydl_opts) as ydl:
                # Run in thread to avoid blocking
                info = await asyncio.to_thread(ydl.extract_info, url, download=False)

                return {
                    'platform': self._detect_platform(url),
                    'external_id': info.get('id'),
                    'title': info.get('title'),
                    'description': info.get('description'),
                    'duration': info.get('duration'),
                    'thumbnail_url': info.get('thumbnail'),
                    'channel_name': info.get('uploader') or info.get('channel'),
                    'view_count': info.get('view_count'),
                    'upload_date': info.get('upload_date'),
                    'url': url
                }
        except Exception as e:
            raise VideoProcessingError(f"Failed to extract video info: {str(e)}")

    async def extract_transcript(self, platform: str, external_id: str) -> List[Dict]:
        """Extract transcript from video"""

        if platform == "youtube":
            return await self._extract_youtube_transcript(external_id)
        else:
            # For other platforms, we'll need to implement custom extraction
            # or use Whisper for audio transcription
            raise VideoProcessingError(f"Transcript extraction not yet supported for {platform}")

    async def _extract_youtube_transcript(self, video_id: str) -> List[Dict]:
        """Extract transcript from YouTube video"""

        try:
            # Try to get transcript using youtube-transcript-api
            transcript_list = await asyncio.to_thread(
                YouTubeTranscriptApi.get_transcript,
                video_id,
                languages=['en', 'en-US', 'en-GB']
            )

            segments = []
            for entry in transcript_list:
                segments.append({
                    'start_time': entry['start'],
                    'end_time': entry['start'] + entry['duration'],
                    'text': entry['text'],
                    'confidence': 1.0  # YouTube transcripts are generally high quality
                })

            return segments

        except Exception as e:
            # Fallback to Whisper if YouTube transcript is not available
            raise VideoProcessingError(f"Failed to extract YouTube transcript: {str(e)}")

    def _detect_platform(self, url: str) -> str:
        """Detect video platform from URL"""

        domain = urlparse(url).netloc.lower()

        if 'youtube.com' in domain or 'youtu.be' in domain:
            return 'youtube'
        elif 'tiktok.com' in domain:
            return 'tiktok'
        elif 'linkedin.com' in domain:
            return 'linkedin'
        elif 'facebook.com' in domain or 'fb.watch' in domain:
            return 'facebook'
        elif 'instagram.com' in domain:
            return 'instagram'
        else:
            return 'unknown'

    def extract_youtube_id(self, url: str) -> Optional[str]:
        """Extract YouTube video ID from URL"""

        patterns = [
            r'(?:youtube\.com\/watch\?v=|youtu\.be\/)([^&\n?#]+)',
            r'youtube\.com\/embed\/([^&\n?#]+)',
            r'youtube\.com\/v\/([^&\n?#]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, url)
            if match:
                return match.group(1)

        return None
```

### 4. FastAPI Endpoints

```python
# backend/app/api/v1/videos.py
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlmodel import Session, select
from typing import List, Optional

from app.core.database import get_db
from app.core.security import get_current_user
from app.models.user import User
from app.models.video import Video, Transcript, TranscriptSegment
from app.schemas.video import VideoAnalysisRequest, VideoAnalysisResponse, TranscriptResponse
from app.services.video_processor import VideoProcessor, VideoProcessingError
from app.services.ai_service import VideoAIService

router = APIRouter(prefix="/videos", tags=["videos"])

@router.post("/analyze", response_model=VideoAnalysisResponse)
async def analyze_video(
    request: VideoAnalysisRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Analyze video from URL - extract metadata, transcript, and generate AI summary
    """

    processor = VideoProcessor()
    ai_service = VideoAIService()

    try:
        # Extract video information
        video_info = await processor.extract_video_info(request.url)

        # Check if video already exists
        existing_video = db.exec(
            select(Video).where(Video.external_id == video_info['external_id'])
        ).first()

        if existing_video:
            return VideoAnalysisResponse(
                video_id=existing_video.id,
                title=existing_video.title,
                duration=existing_video.duration,
                status="already_processed"
            )

        # Create new video record
        video = Video(**video_info)
        db.add(video)
        db.commit()
        db.refresh(video)

        # Extract transcript in background
        background_tasks.add_task(
            process_video_transcript,
            video.id,
            video.platform,
            video.external_id
        )

        return VideoAnalysisResponse(
            video_id=video.id,
            title=video.title,
            duration=video.duration,
            status="processing"
        )

    except VideoProcessingError as e:
        raise HTTPException(status_code=422, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="Internal server error")

@router.get("/{video_id}/transcript", response_model=TranscriptResponse)
async def get_transcript(
    video_id: int,
    start_time: Optional[float] = None,
    end_time: Optional[float] = None,
    db: Session = Depends(get_db)
):
    """
    Get transcript segments for a video with optional time filtering
    """

    # Get video and transcript
    video = db.get(Video, video_id)
    if not video:
        raise HTTPException(status_code=404, detail="Video not found")

    if not video.transcript:
        raise HTTPException(status_code=404, detail="Transcript not available")

    # Build query for transcript segments
    query = select(TranscriptSegment).where(
        TranscriptSegment.transcript_id == video.transcript.id
    )

    if start_time is not None:
        query = query.where(TranscriptSegment.start_time >= start_time)

    if end_time is not None:
        query = query.where(TranscriptSegment.end_time <= end_time)

    query = query.order_by(TranscriptSegment.start_time)

    segments = db.exec(query).all()

    return TranscriptResponse(
        video_id=video_id,
        segments=[
            {
                "id": seg.id,
                "start_time": seg.start_time,
                "end_time": seg.end_time,
                "text": seg.text,
                "confidence": seg.confidence
            }
            for seg in segments
        ]
    )

async def process_video_transcript(video_id: int, platform: str, external_id: str):
    """Background task to process video transcript"""

    processor = VideoProcessor()
    ai_service = VideoAIService()

    try:
        # Extract transcript
        segments = await processor.extract_transcript(platform, external_id)

        # Save transcript to database
        with get_db() as db:
            video = db.get(Video, video_id)
            if not video:
                return

            # Create transcript record
            transcript = Transcript(
                video_id=video_id,
                source="youtube-api" if platform == "youtube" else "whisper",
                confidence_score=sum(seg.get('confidence', 0) for seg in segments) / len(segments)
            )
            db.add(transcript)
            db.commit()
            db.refresh(transcript)

            # Create transcript segments
            for seg_data in segments:
                segment = TranscriptSegment(
                    transcript_id=transcript.id,
                    **seg_data
                )
                db.add(segment)

            db.commit()

            # Generate AI summary
            transcript_texts = [seg['text'] for seg in segments]
            summary = await ai_service.generate_summary(video.title, transcript_texts)

            # Update video with summary (you might want to add a summary field to Video model)
            # video.ai_summary = summary
            # db.commit()

    except Exception as e:
        # Log error and update video status
        print(f"Error processing transcript for video {video_id}: {e}")
```

This implementation provides a solid foundation for the InsightStream platform. The code includes:

1. **Comprehensive database models** with proper relationships and indexing
2. **AI service integration** using Agno agents with structured outputs
3. **Video processing service** with multi-platform support
4. **FastAPI endpoints** with proper error handling and background tasks
5. **Type safety** throughout with SQLModel and Pydantic
6. **Scalable architecture** with clear separation of concerns

The next steps would be to implement the frontend React components, add more sophisticated AI features, and set up the development environment with Docker.
