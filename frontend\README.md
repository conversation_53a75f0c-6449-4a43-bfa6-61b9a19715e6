# InsightStream Frontend

> **React 18+ frontend with TypeScript and free browser-based voice features**

This is the frontend application for InsightStream, built with React 18+, TypeScript 5+, and Vite, featuring a modern UI with comprehensive voice interaction capabilities using free browser APIs.

## 🚀 Quick Start

### Prerequisites

- **Node.js 18+** ([download](https://nodejs.org/))
- **npm** or **yarn** package manager
- **Modern browser** with Web Speech API support

### Setup

```bash
# Install dependencies with current versions
npm install

# Install Tailwind CSS v4 (2025 latest with Vite plugin)
npm install tailwindcss @tailwindcss/vite

# Install core speech packages (free)
npm install react-speech-recognition react-speakup

# Install additional UI packages
npm install @headlessui/react @heroicons/react zustand @tanstack/react-query

# Start development server
npm run dev

# The frontend will be available at http://localhost:3000
```

### Access Points

- **Development Server**: <http://localhost:3000>
- **Production Build**: `npm run build`
- **Preview Build**: `npm run preview`

## 🏗️ Architecture

### Project Structure

```plaintext
frontend/
├── src/
│   ├── main.tsx             # Application entry point
│   ├── App.tsx              # Main app component
│   ├── components/          # Reusable UI components
│   │   ├── common/          # Common UI components
│   │   │   ├── Button.tsx   # Button component
│   │   │   ├── Modal.tsx    # Modal component
│   │   │   └── Layout.tsx   # Layout wrapper
│   │   ├── Voice/           # Voice interaction components
│   │   │   ├── VoiceCommands.tsx    # Voice command interface
│   │   │   ├── AudioButton.tsx      # Audio playback button
│   │   │   ├── VoiceSettings.tsx    # Voice configuration
│   │   │   └── VoiceNoteRecorder.tsx # Voice note recording
│   │   ├── Video/           # Video-related components
│   │   │   ├── VideoPlayer.tsx      # Video player component
│   │   │   ├── VideoUpload.tsx      # Video upload interface
│   │   │   ├── VideoList.tsx        # Video library
│   │   │   └── VideoAnalysis.tsx    # Video analysis display
│   │   ├── AI/              # AI interaction components
│   │   │   ├── ChatInterface.tsx    # AI chat component
│   │   │   ├── MessageBubble.tsx    # Chat message display
│   │   │   ├── QueryInput.tsx       # Query input component
│   │   │   └── ResponseDisplay.tsx  # AI response display
│   │   ├── Notes/           # Note management components
│   │   │   ├── NoteEditor.tsx       # Rich text note editor
│   │   │   ├── NoteList.tsx         # Notes listing
│   │   │   ├── NoteCard.tsx         # Individual note card
│   │   │   └── NoteTags.tsx         # Note tagging system
│   │   └── Dashboard/       # Dashboard components
│   │       ├── Overview.tsx         # Dashboard overview
│   │       ├── Analytics.tsx        # Usage analytics
│   │       └── RecentActivity.tsx   # Recent activity feed
│   ├── hooks/               # Custom React hooks
│   │   ├── useSpeechRecognition.ts  # Speech-to-text hook
│   │   ├── useTextToSpeech.ts       # Text-to-speech hook
│   │   ├── useVideoPlayer.ts        # Video player controls
│   │   ├── useAIChat.ts             # AI chat functionality
│   │   ├── useNotes.ts              # Note management
│   │   └── useAuth.ts               # Authentication
│   ├── stores/              # Zustand state stores
│   │   ├── authStore.ts             # Authentication state
│   │   ├── videoStore.ts            # Video management state
│   │   ├── notesStore.ts            # Notes state
│   │   ├── voiceStore.ts            # Voice settings state
│   │   └── uiStore.ts               # UI state (modals, etc.)
│   ├── services/            # API and external services
│   │   ├── api.ts                   # API client configuration
│   │   ├── auth.ts                  # Authentication service
│   │   ├── videos.ts                # Video API service
│   │   ├── ai.ts                    # AI interaction service
│   │   ├── notes.ts                 # Notes API service
│   │   └── speech.ts                # Speech service utilities
│   ├── utils/               # Utility functions
│   │   ├── speechSupport.ts         # Browser speech support detection
│   │   ├── videoUtils.ts            # Video processing utilities
│   │   ├── formatters.ts            # Data formatting utilities
│   │   ├── validators.ts            # Input validation
│   │   └── constants.ts             # Application constants
│   ├── types/               # TypeScript type definitions
│   │   ├── api.ts                   # API response types
│   │   ├── video.ts                 # Video-related types
│   │   ├── note.ts                  # Note types
│   │   ├── voice.ts                 # Voice feature types
│   │   └── user.ts                  # User types
│   ├── styles/              # Global styles and themes
│   │   ├── globals.css              # Global CSS styles
│   │   ├── components.css           # Component-specific styles
│   │   └── tailwind.css             # Tailwind CSS imports
│   └── assets/              # Static assets
│       ├── images/                  # Image assets
│       ├── icons/                   # Icon assets
│       └── sounds/                  # Audio assets
├── public/                  # Public static files
│   ├── index.html           # HTML template
│   ├── favicon.ico          # Favicon
│   └── manifest.json        # PWA manifest
├── tests/                   # Test files
│   ├── components/          # Component tests
│   ├── hooks/               # Hook tests
│   ├── utils/               # Utility tests
│   └── e2e/                 # End-to-end tests
├── package.json             # Dependencies and scripts
├── vite.config.ts           # Vite configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
├── eslint.config.js         # ESLint configuration
├── prettier.config.js       # Prettier configuration
├── requirements.txt         # Package reference (for documentation)
└── README.md               # This file
```

## 🎤 Voice Features

### Free Browser-Based Speech APIs

InsightStream uses **100% free browser APIs** for voice interaction:

#### Speech-to-Text (STT)

- **Browser SpeechRecognition API**: No API keys required
- **Real-time transcription**: Live speech-to-text conversion
- **Multiple languages**: Support for 50+ languages
- **Interim results**: See text as you speak

#### Text-to-Speech (TTS)

- **Browser SpeechSynthesis API**: Completely free
- **Multiple voices**: System and browser voices
- **Customizable**: Rate, pitch, volume control
- **Cross-platform**: Works on desktop and mobile

#### Voice Components

```typescript
// Voice Commands Component
<VoiceCommands
  onCommand={(text) => handleVoiceCommand(text)}
  language="en-US"
  continuous={false}
/>

// Audio Button Component
<AudioButton
  text="AI response text to read aloud"
  variant="ghost"
  size="sm"
/>

// Voice Note Recorder
<VoiceNoteRecorder
  onSave={(note) => saveVoiceNote(note)}
  maxDuration={300}
/>
```

### Voice Integration Examples

```typescript
// AI Chat with Voice
const ChatInterface = () => {
  const { sendMessage } = useAIChat();

  const handleVoiceCommand = async (command: string) => {
    const response = await sendMessage(command);
    // Response can be read aloud with AudioButton
  };

  return (
    <div>
      <VoiceCommands onCommand={handleVoiceCommand} />
      {/* Chat messages with audio buttons */}
    </div>
  );
};
```

## 🛠️ Technology Stack

### Core Framework

- **React 18+**: Latest React with concurrent features
- **TypeScript 5+**: Full type safety and modern TS features
- **Vite**: Fast development and optimized builds
- **React Router**: Client-side routing

### State Management

- **Zustand**: Lightweight state management
- **TanStack Query**: Server state and caching
- **React Hook Form**: Form state management

### UI and Styling

- **Tailwind CSS v4**: Latest utility-first CSS framework with Vite plugin
- **Headless UI**: Unstyled, accessible UI components
- **Heroicons**: Beautiful SVG icons
- **Framer Motion**: Smooth animations

#### Tailwind CSS v4 Setup (2025)

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'

export default defineConfig({
  plugins: [
    react(),
    tailwindcss(), // New v4 Vite plugin
  ],
})
```

```css
/* src/index.css */
@import "tailwindcss";
```

### Voice Features (Free)

- **react-speech-recognition**: Speech-to-text wrapper
- **react-speakup**: Text-to-speech utilities
- **Web Speech API**: Browser-native speech capabilities

### Development Tools

- **ESLint**: Code linting and quality
- **Prettier**: Code formatting
- **Vitest**: Fast unit testing
- **Playwright**: End-to-end testing

## 🔧 Development

### Environment Variables

```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:8000
VITE_API_VERSION=v1

# Voice Configuration (Browser-based - Free)
VITE_VOICE_ENABLED=true
VITE_VOICE_PROVIDER=browser
VITE_TTS_RATE=1.0
VITE_TTS_PITCH=1.0
VITE_TTS_VOLUME=1.0
VITE_STT_LANGUAGE=en-US

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_PWA=true
VITE_DEBUG_MODE=false
```

### Development Commands

```bash
# Development server with hot reload
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview

# Run tests
npm test                    # Unit tests
npm run test:watch          # Watch mode
npm run test:coverage       # With coverage
npm run test:e2e           # End-to-end tests

# Code quality
npm run lint               # ESLint
npm run lint:fix           # Auto-fix issues
npm run format             # Prettier formatting
npm run type-check         # TypeScript checking

# Dependencies
npm install                # Install dependencies
npm update                 # Update packages
npm audit                  # Security audit
```

## 🧪 Testing

### Test Structure

```bash
tests/
├── components/            # Component tests
│   ├── Voice/            # Voice component tests
│   ├── Video/            # Video component tests
│   └── AI/               # AI component tests
├── hooks/                # Custom hook tests
│   ├── useSpeechRecognition.test.ts
│   ├── useTextToSpeech.test.ts
│   └── useAIChat.test.ts
├── utils/                # Utility function tests
│   ├── speechSupport.test.ts
│   └── validators.test.ts
└── e2e/                  # End-to-end tests
    ├── voice-features.spec.ts
    ├── video-upload.spec.ts
    └── ai-chat.spec.ts
```

### Running Tests

```bash
# All tests
npm test

# Component tests
npm run test:components

# Hook tests
npm run test:hooks

# E2E tests
npm run test:e2e

# Coverage report
npm run test:coverage
```

## 🎨 UI Components

### Voice Components

```typescript
// Voice command interface
<VoiceCommands
  onCommand={handleCommand}
  disabled={isProcessing}
  language="en-US"
/>

// Audio playback button
<AudioButton
  text="Text to read aloud"
  size="sm"
  variant="ghost"
/>

// Voice settings panel
<VoiceSettings
  onSettingsChange={updateVoiceSettings}
  showAdvanced={true}
/>
```

### Video Components

```typescript
// Video player with controls
<VideoPlayer
  src={videoUrl}
  onTimeUpdate={handleTimeUpdate}
  showTranscript={true}
/>

// Video upload interface
<VideoUpload
  onUpload={handleVideoUpload}
  acceptedFormats={['mp4', 'webm', 'mov']}
  maxSize="100MB"
/>
```

### AI Components

```typescript
// AI chat interface
<ChatInterface
  onMessage={sendMessage}
  enableVoice={true}
  showTyping={isTyping}
/>

// AI response display
<ResponseDisplay
  response={aiResponse}
  enableAudio={true}
  showSources={true}
/>
```

## 🚀 Deployment

### Production Build

```bash
# Build for production
npm run build

# Preview production build locally
npm run preview

# Build with environment
NODE_ENV=production npm run build
```

### Environment-Specific Builds

```bash
# Development build
npm run build:dev

# Staging build
npm run build:staging

# Production build
npm run build:prod
```

### Static Hosting

The built application can be deployed to any static hosting service:

- **Vercel**: `vercel --prod`
- **Netlify**: `netlify deploy --prod`
- **AWS S3**: Upload `dist/` folder
- **GitHub Pages**: Deploy `dist/` folder
- **Docker**: Use included Dockerfile

## 📱 Progressive Web App (PWA)

### PWA Features

- **Offline Support**: Works without internet connection
- **Install Prompt**: Add to home screen
- **Background Sync**: Sync data when online
- **Push Notifications**: Real-time updates

### PWA Configuration

```typescript
// vite.config.ts
import { VitePWA } from 'vite-plugin-pwa';

export default defineConfig({
  plugins: [
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      manifest: {
        name: 'InsightStream',
        short_name: 'InsightStream',
        description: 'Unlock the Knowledge Within Video',
        theme_color: '#3b82f6',
        icons: [
          {
            src: 'icon-192.png',
            sizes: '192x192',
            type: 'image/png'
          }
        ]
      }
    })
  ]
});
```

## 🔒 Security

### Content Security Policy

```html
<!-- index.html -->
<meta http-equiv="Content-Security-Policy"
      content="default-src 'self';
               script-src 'self' 'unsafe-inline';
               style-src 'self' 'unsafe-inline';
               media-src 'self' blob: data:;">
```

### Input Validation

```typescript
// utils/validators.ts
export const validateVideoUrl = (url: string): boolean => {
  const urlPattern = /^https?:\/\/.+/;
  return urlPattern.test(url);
};

export const sanitizeInput = (input: string): string => {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
};
```

## 🌐 Internationalization

### i18n Setup

```typescript
// i18n/index.ts
import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';

i18n
  .use(initReactI18next)
  .init({
    resources: {
      en: { translation: require('./locales/en.json') },
      es: { translation: require('./locales/es.json') },
      fr: { translation: require('./locales/fr.json') }
    },
    lng: 'en',
    fallbackLng: 'en'
  });
```

### Usage in Components

```typescript
import { useTranslation } from 'react-i18next';

const Component = () => {
  const { t } = useTranslation();

  return (
    <div>
      <h1>{t('welcome.title')}</h1>
      <p>{t('welcome.description')}</p>
    </div>
  );
};
```

## 📊 Performance

### Optimization Strategies

- **Code Splitting**: Lazy load components
- **Bundle Analysis**: Analyze bundle size
- **Image Optimization**: WebP format, lazy loading
- **Caching**: Service worker caching
- **Tree Shaking**: Remove unused code

### Performance Monitoring

```typescript
// utils/performance.ts
export const measurePerformance = (name: string, fn: () => void) => {
  const start = performance.now();
  fn();
  const end = performance.now();
  console.log(`${name} took ${end - start} milliseconds`);
};

// Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals';

getCLS(console.log);
getFID(console.log);
getFCP(console.log);
getLCP(console.log);
getTTFB(console.log);
```

## 🤝 Contributing

### Development Workflow

1. **Setup Environment**

   ```bash
   npm install
   npm run dev
   ```

2. **Create Feature Branch**

   ```bash
   git checkout -b feature/voice-improvements
   ```

3. **Development Standards**
   - Write TypeScript with strict types
   - Add tests for new components
   - Follow ESLint and Prettier rules
   - Update documentation

4. **Testing**

   ```bash
   npm run test
   npm run test:e2e
   npm run lint
   npm run type-check
   ```

5. **Submit Pull Request**
   - Include component tests
   - Update relevant documentation
   - Ensure all checks pass

### Code Standards

- **TypeScript**: Strict mode enabled
- **Components**: Functional components with hooks
- **Styling**: Tailwind CSS utility classes
- **Testing**: Jest + Testing Library + Playwright
- **Accessibility**: WCAG 2.1 AA compliance

## 📞 Support

### Common Issues

#### Voice Features Not Working

```typescript
// Check browser support
import { checkSpeechSupport } from './utils/speechSupport';

const support = checkSpeechSupport();
if (!support.speechToText) {
  console.log('Speech recognition not supported');
}
```

#### HTTPS Required

- Speech Recognition requires HTTPS in production
- Use `localhost` for development
- Deploy to HTTPS-enabled hosting

#### Microphone Permissions

- Browser will prompt for microphone access
- Users must explicitly grant permission
- Provide clear instructions and fallbacks

### Troubleshooting

- **Build Issues**: Check Node.js version (18+)
- **Type Errors**: Run `npm run type-check`
- **Linting**: Run `npm run lint:fix`
- **Dependencies**: Run `npm audit fix`

### Getting Help

- **Voice Features**: See [Speech Implementation Guide](../docs/SPEECH_IMPLEMENTATION_GUIDE.md)
- **API Integration**: Check backend API documentation
- **UI Components**: Reference component documentation
- **Performance**: Use browser dev tools profiler

---

**InsightStream Frontend** - *Modern React app with free voice features* 🎤✨

This implementation provides a modern, accessible, and feature-rich frontend for InsightStream with comprehensive voice capabilities.
