# Testing Framework
pytest
pytest-asyncio
pytest-cov
pytest-mock
pytest-xdist
pytest-benchmark
pytest-timeout

# Code Quality and Linting
ruff
mypy
black
isort
flake8
bandit

# Pre-commit Hooks
pre-commit

# Type Checking
types-requests
types-redis
types-pillow
types-beautifulsoup4
types-python-jose

# Development Tools
ipython
jupyter
notebook

# Debugging
pdb++
icecream
snoop

# Load Testing
locust
httpx

# Database Testing
pytest-postgresql
sqlalchemy-utils

# Mock and Fixtures
responses
httpretty
freezegun
time-machine

# Documentation
mkdocs
mkdocs-material
mkdocs-mermaid2-plugin

# Development Utilities
watchdog
python-dotenv
rich-cli

# Performance Profiling
line-profiler
memory-profiler
py-spy
scalene

# Security Testing
safety
semgrep

# API Testing
httpie
curl-cffi

# Development Server
uvicorn[standard]
gunicorn

# Environment Management
python-decouple

# Development Database
sqlite-utils

# Code Coverage
coverage
codecov

# Dependency Management
pip-tools
pipdeptree

# Development Monitoring
flask-debugtoolbar

# Code Formatting
autopep8
yapf

# Import Sorting
reorder-python-imports

# Documentation Generation
sphinx
sphinx-rtd-theme

# API Documentation
redoc-cli

# Development Utilities
click
typer

# File Watching
watchfiles

# Development Logging
colorlog
loguru

# Development Configuration
python-dotenv
environs

# Development Database Tools
alembic-utils
sqlalchemy-migrate

# Development Testing
factory-boy
faker
mimesis

# Development Debugging
pudb
wdb

# Development Profiling
pyinstrument
cprofilev

# Development Utilities
tqdm
colorama
termcolor

# Development API Tools
httpx-oauth
authlib

# Development Validation
cerberus
marshmallow

# Development Serialization
orjson
ujson

# Development Utilities
more-itertools
toolz
funcy

# Development Async Tools
aiohttp
aioredis
asyncpg-utils

# Development File Handling
pathlib2
send2trash

# Development String Processing
fuzzywuzzy
python-levenshtein

# Development Date/Time
arrow
pendulum
dateutil

# Development UUID
shortuuid
uuid6

# Development Hashing
hashids
bcrypt

# Development Encryption
fernet
nacl

# Development Compression
zstandard
lz4

# Development Parsing
parse
parsimonious

# Development Validation
voluptuous
schema

# Development Caching
joblib
shelve

# Development Concurrency
concurrent-futures
threading2

# Development Process Management
psutil
sh

# Development System Info
platform
distro

# Development Network
netifaces
psutil

# Development File System
pathtools
scandir
