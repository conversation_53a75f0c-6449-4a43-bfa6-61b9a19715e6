# Speech Implementation Guide

> **Free Browser-Based Speech-to-Text and Text-to-Speech for InsightStream**

This guide provides a complete implementation of voice features using the **Web Speech API** - completely free, no API keys required, and privacy-first.

## 🎯 Overview

InsightStream uses browser-native speech APIs to provide:

- **Speech-to-Text (STT)**: Convert user voice commands to text
- **Text-to-Speech (TTS)**: Read AI responses and notes aloud
- **Voice Notes**: Record and playback voice notes
- **Voice Commands**: Control the app with natural speech

## 🛠️ Technology Stack

### Frontend Dependencies

```bash
npm install react-speech-recognition react-speakup
```

### Browser APIs Used

- **SpeechRecognition API**: For speech-to-text conversion
- **SpeechSynthesis API**: For text-to-speech conversion
- **MediaDevices API**: For microphone access

## 📦 Installation

### 1. Install Dependencies

```bash
cd frontend
npm install react-speech-recognition react-speakup
```

### 2. Browser Support Check

```typescript
// utils/speechSupport.ts
export const checkSpeechSupport = () => {
  const hasSTT = 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
  const hasTTS = 'speechSynthesis' in window;

  return {
    speechToText: hasSTT,
    textToSpeech: hasTTS,
    fullSupport: hasSTT && hasTTS
  };
};
```

## 🎤 Speech-to-Text Implementation

### Voice Recognition Hook

```typescript
// hooks/useSpeechRecognition.ts
import { useState, useEffect, useCallback } from 'react';
import SpeechRecognition, { useSpeechRecognition } from 'react-speech-recognition';

interface UseSpeechRecognitionProps {
  onResult?: (transcript: string) => void;
  onError?: (error: string) => void;
  continuous?: boolean;
  language?: string;
}

export const useSpeechRecognitionHook = ({
  onResult,
  onError,
  continuous = false,
  language = 'en-US'
}: UseSpeechRecognitionProps = {}) => {
  const [isSupported, setIsSupported] = useState(false);

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable
  } = useSpeechRecognition();

  useEffect(() => {
    setIsSupported(browserSupportsSpeechRecognition && isMicrophoneAvailable);
  }, [browserSupportsSpeechRecognition, isMicrophoneAvailable]);

  useEffect(() => {
    if (transcript && onResult) {
      onResult(transcript);
    }
  }, [transcript, onResult]);

  const startListening = useCallback(() => {
    if (!isSupported) {
      onError?.('Speech recognition not supported');
      return;
    }

    SpeechRecognition.startListening({
      continuous,
      language,
      interimResults: true
    });
  }, [isSupported, continuous, language, onError]);

  const stopListening = useCallback(() => {
    SpeechRecognition.stopListening();
  }, []);

  const toggleListening = useCallback(() => {
    if (listening) {
      stopListening();
    } else {
      startListening();
    }
  }, [listening, startListening, stopListening]);

  return {
    transcript,
    listening,
    isSupported,
    startListening,
    stopListening,
    toggleListening,
    resetTranscript
  };
};
```

## 🔊 Text-to-Speech Implementation

### TTS Hook

```typescript
// hooks/useTextToSpeech.ts
import { useState, useCallback, useEffect } from 'react';

interface UseTextToSpeechProps {
  rate?: number;
  pitch?: number;
  volume?: number;
  voice?: string;
  language?: string;
}

export const useTextToSpeech = ({
  rate = 1,
  pitch = 1,
  volume = 1,
  voice,
  language = 'en-US'
}: UseTextToSpeechProps = {}) => {
  const [isSupported, setIsSupported] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [voices, setVoices] = useState<SpeechSynthesisVoice[]>([]);

  useEffect(() => {
    setIsSupported('speechSynthesis' in window);

    if ('speechSynthesis' in window) {
      const loadVoices = () => {
        const availableVoices = speechSynthesis.getVoices();
        setVoices(availableVoices);
      };

      loadVoices();
      speechSynthesis.onvoiceschanged = loadVoices;
    }
  }, []);

  const speak = useCallback((text: string) => {
    if (!isSupported || !text.trim()) return;

    speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    utterance.rate = rate;
    utterance.pitch = pitch;
    utterance.volume = volume;
    utterance.lang = language;

    if (voice) {
      const selectedVoice = voices.find(v => v.name === voice);
      if (selectedVoice) {
        utterance.voice = selectedVoice;
      }
    }

    utterance.onstart = () => setIsSpeaking(true);
    utterance.onend = () => setIsSpeaking(false);
    utterance.onerror = () => setIsSpeaking(false);

    speechSynthesis.speak(utterance);
  }, [isSupported, rate, pitch, volume, language, voice, voices]);

  const stop = useCallback(() => {
    if (isSupported) {
      speechSynthesis.cancel();
      setIsSpeaking(false);
    }
  }, [isSupported]);

  return {
    speak,
    stop,
    isSpeaking,
    isSupported,
    voices
  };
};
```

## 🎮 Voice Components

### Voice Commands Component

```typescript
// components/Voice/VoiceCommands.tsx
import React, { useState } from 'react';
import { useSpeechRecognitionHook } from '../../hooks/useSpeechRecognition';

interface VoiceCommandsProps {
  onCommand: (command: string) => void;
  disabled?: boolean;
}

export const VoiceCommands: React.FC<VoiceCommandsProps> = ({
  onCommand,
  disabled = false
}) => {
  const [isProcessing, setIsProcessing] = useState(false);

  const {
    transcript,
    listening,
    isSupported,
    toggleListening,
    resetTranscript
  } = useSpeechRecognitionHook({
    onResult: (result) => {
      if (result.trim()) {
        setIsProcessing(true);
        onCommand(result);
        resetTranscript();
        setTimeout(() => setIsProcessing(false), 1000);
      }
    },
    continuous: false,
    language: 'en-US'
  });

  if (!isSupported) {
    return (
      <div className="text-gray-500 text-sm">
        Voice commands not supported in this browser
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2">
      <button
        onClick={toggleListening}
        disabled={disabled || isProcessing}
        className={`px-3 py-2 rounded text-sm ${
          listening
            ? 'bg-red-500 text-white'
            : 'bg-blue-500 text-white hover:bg-blue-600'
        }`}
      >
        {listening ? '🛑 Stop' : '🎤 Voice Command'}
      </button>

      {listening && (
        <div className="flex items-center space-x-2">
          <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          <span className="text-sm text-gray-600">Listening...</span>
        </div>
      )}

      {transcript && (
        <div className="text-sm text-gray-700 italic">
          "{transcript}"
        </div>
      )}
    </div>
  );
};
```

### Audio Button Component

```typescript
// components/Voice/AudioButton.tsx
import React from 'react';
import { useTextToSpeech } from '../../hooks/useTextToSpeech';

interface AudioButtonProps {
  text: string;
  disabled?: boolean;
}

export const AudioButton: React.FC<AudioButtonProps> = ({
  text,
  disabled = false
}) => {
  const { speak, stop, isSpeaking, isSupported } = useTextToSpeech();

  const handleClick = () => {
    if (isSpeaking) {
      stop();
    } else {
      speak(text);
    }
  };

  if (!isSupported || !text.trim()) {
    return null;
  }

  return (
    <button
      onClick={handleClick}
      disabled={disabled}
      className="p-1 text-gray-500 hover:text-blue-500"
      title={isSpeaking ? 'Stop audio' : 'Play audio'}
    >
      {isSpeaking ? '🔇' : '🔊'}
    </button>
  );
};
```

## 🔗 Integration Examples

### AI Chat with Voice

```typescript
// components/AIChat/ChatInterface.tsx
import React, { useState } from 'react';
import { VoiceCommands } from '../Voice/VoiceCommands';
import { AudioButton } from '../Voice/AudioButton';

export const ChatInterface: React.FC = () => {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<Array<{id: string, content: string, role: string}>>([]);

  const handleVoiceCommand = async (command: string) => {
    setMessage(command);
    // Send to AI and get response
    const response = await sendToAI(command);
    setMessages(prev => [...prev,
      { id: Date.now().toString(), content: command, role: 'user' },
      { id: (Date.now() + 1).toString(), content: response, role: 'assistant' }
    ]);
    setMessage('');
  };

  return (
    <div className="space-y-4">
      {/* Messages */}
      <div className="space-y-2">
        {messages.map((msg) => (
          <div key={msg.id} className="flex items-start space-x-2">
            <div className="flex-1">
              <p className="text-sm">{msg.content}</p>
            </div>
            {msg.role === 'assistant' && (
              <AudioButton text={msg.content} />
            )}
          </div>
        ))}
      </div>

      {/* Input */}
      <div className="space-y-2">
        <input
          type="text"
          value={message}
          onChange={(e) => setMessage(e.target.value)}
          placeholder="Ask about the video..."
          className="w-full p-2 border rounded"
        />
        <VoiceCommands onCommand={handleVoiceCommand} />
      </div>
    </div>
  );
};
```

## 🔧 Configuration

### Environment Variables

```bash
# Voice Configuration (Browser-based - Free)
VITE_VOICE_ENABLED=true
VITE_VOICE_PROVIDER=browser
VITE_TTS_RATE=1.0
VITE_TTS_PITCH=1.0
VITE_TTS_VOLUME=1.0
VITE_STT_LANGUAGE=en-US
```

## 🐛 Troubleshooting

### Common Issues

1. **HTTPS Required**: Speech Recognition requires HTTPS (except localhost)
2. **Microphone Permissions**: User must grant microphone access
3. **Browser Support**: Works best in Chrome, Edge, Safari
4. **Mobile Limitations**: iOS requires user gesture to start speech

### Error Handling

```typescript
// utils/speechErrorHandler.ts
export const handleSpeechError = (error: string) => {
  switch (error) {
    case 'not-allowed':
      return 'Microphone access denied. Please allow microphone access.';
    case 'no-speech':
      return 'No speech detected. Please try again.';
    case 'network':
      return 'Network error. Please check your connection.';
    default:
      return 'Speech recognition error. Please try again.';
  }
};
```

## 🔒 Privacy & Security

- **No data transmission**: All speech processing happens locally
- **No storage**: Speech data is not stored unless explicitly saved
- **User control**: Users can disable voice features anytime
- **Transparent permissions**: Clear indication when microphone is active

This implementation provides a complete, free, and privacy-first voice solution for InsightStream.
