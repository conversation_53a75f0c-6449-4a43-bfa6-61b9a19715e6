# Comprehensive API Specifications

## API Architecture Overview

### Base Configuration

- **Base URL**: `https://api.videointeraction.com/api/v1`
- **Authentication**: JWT Bearer tokens with refresh mechanism
- **Rate Limiting**: 100 requests/minute per user, 1000/minute for premium
- **Response Format**: JSON with consistent error handling
- **Versioning**: URL-based versioning (`/api/v1`, `/api/v2`)

### Standard Response Format

```json
{
  "success": true,
  "data": {},
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

### Error Response Format

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid video URL provided",
    "details": {
      "field": "url",
      "reason": "URL format is not supported"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z",
  "request_id": "req_123456789"
}
```

## Authentication Endpoints

### POST /auth/register

**Description**: Register a new user account

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "username": "johndoe",
  "password": "SecurePassword123!",
  "full_name": "John Doe"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "johndoe",
      "full_name": "John Doe",
      "is_verified": false,
      "created_at": "2024-01-15T10:30:00Z"
    },
    "verification_sent": true
  }
}
```

### POST /auth/login

**Description**: Authenticate user and receive tokens

**Request Body**:

```json
{
  "email": "<EMAIL>",
  "password": "SecurePassword123!"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 1800,
    "user": {
      "id": 1,
      "email": "<EMAIL>",
      "username": "johndoe"
    }
  }
}
```

### POST /auth/refresh

**Description**: Refresh access token using refresh token

**Request Body**:

```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

## Video Processing Endpoints

### POST /videos/analyze

**Description**: Analyze video from URL and extract metadata/transcript

**Request Body**:

```json
{
  "url": "https://youtube.com/watch?v=dQw4w9WgXcQ",
  "options": {
    "extract_transcript": true,
    "generate_summary": true,
    "analyze_content": true,
    "language": "en"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "video": {
      "id": 123,
      "platform": "youtube",
      "external_id": "dQw4w9WgXcQ",
      "title": "Never Gonna Give You Up",
      "duration": 212.0,
      "thumbnail_url": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
      "processing_status": "processing"
    },
    "estimated_completion": "2024-01-15T10:32:00Z"
  }
}
```

### GET /videos/{video_id}

**Description**: Get video details and processing status

**Response**:

```json
{
  "success": true,
  "data": {
    "video": {
      "id": 123,
      "platform": "youtube",
      "title": "Never Gonna Give You Up",
      "duration": 212.0,
      "processing_status": "completed",
      "ai_summary": "A classic 1980s pop song with themes of commitment and loyalty...",
      "key_topics": ["music", "1980s", "pop culture", "Rick Astley"],
      "transcript_available": true,
      "created_at": "2024-01-15T10:30:00Z"
    }
  }
}
```

### GET /videos/{video_id}/transcript

**Description**: Get video transcript with optional time filtering

**Query Parameters**:

- `start_time` (optional): Start time in seconds
- `end_time` (optional): End time in seconds
- `format` (optional): `json` (default) or `srt` or `vtt`

**Response**:

```json
{
  "success": true,
  "data": {
    "transcript": {
      "id": 456,
      "video_id": 123,
      "language": "en",
      "confidence_score": 0.95,
      "segments": [
        {
          "id": 1,
          "start_time": 0.0,
          "end_time": 3.5,
          "text": "We're no strangers to love",
          "confidence": 0.98,
          "word_timestamps": [
            {"word": "We're", "start": 0.0, "end": 0.3},
            {"word": "no", "start": 0.4, "end": 0.6}
          ]
        }
      ]
    }
  }
}
```

## AI Interaction Endpoints

### POST /ai/ask

**Description**: Ask AI question about video content

**Request Body**:

```json
{
  "video_id": 123,
  "question": "What is the main theme of this song?",
  "context": {
    "start_time": 0.0,
    "end_time": 30.0,
    "include_notes": true,
    "agent_type": "qa_agent"
  }
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "interaction": {
      "id": 789,
      "agent_type": "qa_agent",
      "question": "What is the main theme of this song?",
      "answer": "The main theme of this song is unwavering commitment and loyalty in relationships...",
      "confidence_score": 0.92,
      "source_timestamps": [15.2, 45.8, 120.3],
      "related_topics": ["commitment", "loyalty", "relationships"],
      "follow_up_questions": [
        "What musical elements support this theme?",
        "How does this compare to other songs from the same era?"
      ],
      "processing_time": 1.2,
      "created_at": "2024-01-15T10:35:00Z"
    }
  }
}
```

### GET /ai/interactions/{video_id}

**Description**: Get AI interaction history for a video

**Query Parameters**:

- `limit` (optional): Number of interactions to return (default: 20)
- `offset` (optional): Pagination offset (default: 0)
- `agent_type` (optional): Filter by agent type

**Response**:

```json
{
  "success": true,
  "data": {
    "interactions": [
      {
        "id": 789,
        "agent_type": "qa_agent",
        "question": "What is the main theme of this song?",
        "answer": "The main theme is unwavering commitment...",
        "confidence_score": 0.92,
        "created_at": "2024-01-15T10:35:00Z"
      }
    ],
    "pagination": {
      "total": 15,
      "limit": 20,
      "offset": 0,
      "has_more": false
    }
  }
}
```

## Note Management Endpoints

### POST /notes

**Description**: Create a new note with AI assistance

**Request Body**:

```json
{
  "video_id": 123,
  "title": "Key Insights",
  "content": "This song represents the pinnacle of 80s pop culture",
  "timestamp": 45.5,
  "duration": 10.0,
  "content_type": "rich_text",
  "tags": ["music", "analysis"],
  "ai_enhance": true
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "note": {
      "id": 456,
      "title": "Key Insights",
      "content": "This song represents the pinnacle of 80s pop culture...",
      "enhanced_content": "This song represents the pinnacle of 80s pop culture, featuring characteristic synthesizer sounds and memorable hooks that defined the era...",
      "timestamp": 45.5,
      "ai_generated_tags": ["80s music", "pop culture", "synthesizer", "memorable hooks"],
      "suggested_connections": [
        {"note_id": 123, "similarity": 0.85, "reason": "Similar musical analysis"}
      ],
      "created_at": "2024-01-15T10:40:00Z"
    }
  }
}
```

### POST /notes/voice

**Description**: Create note from voice recording

**Request Body** (multipart/form-data):

- `audio`: Audio file (mp3, wav, m4a)
- `video_id`: Video ID
- `timestamp`: Video timestamp
- `language`: Language code (optional)

**Response**:

```json
{
  "success": true,
  "data": {
    "note": {
      "id": 457,
      "content": "This part of the song really showcases the vocal range",
      "transcribed_text": "This part of the song really showcases the vocal range",
      "audio_file_url": "https://storage.example.com/audio/note_457.mp3",
      "confidence_score": 0.94,
      "timestamp": 67.2,
      "created_at": "2024-01-15T10:42:00Z"
    }
  }
}
```

### GET /notes/{video_id}

**Description**: Get all notes for a video

**Query Parameters**:

- `limit` (optional): Number of notes to return
- `sort` (optional): `timestamp` (default), `created_at`, `title`
- `tags` (optional): Filter by tags (comma-separated)

**Response**:

```json
{
  "success": true,
  "data": {
    "notes": [
      {
        "id": 456,
        "title": "Key Insights",
        "content": "This song represents...",
        "timestamp": 45.5,
        "tags": ["music", "analysis"],
        "is_ai_generated": false,
        "created_at": "2024-01-15T10:40:00Z"
      }
    ],
    "total_count": 5
  }
}
```

## Session Management Endpoints

### POST /sessions

**Description**: Create or resume a video session

**Request Body**:

```json
{
  "video_id": 123,
  "session_name": "Learning Session 1"
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "session": {
      "id": 789,
      "video_id": 123,
      "session_name": "Learning Session 1",
      "last_position": 0.0,
      "total_watch_time": 0.0,
      "notes_count": 0,
      "ai_queries_count": 0,
      "started_at": "2024-01-15T10:45:00Z"
    }
  }
}
```

### PUT /sessions/{session_id}

**Description**: Update session progress

**Request Body**:

```json
{
  "last_position": 67.5,
  "session_data": {
    "playback_speed": 1.25,
    "volume": 0.8,
    "transcript_visible": true
  }
}
```

### GET /sessions/history

**Description**: Get user's session history

**Query Parameters**:

- `limit` (optional): Number of sessions to return
- `video_platform` (optional): Filter by platform

**Response**:

```json
{
  "success": true,
  "data": {
    "sessions": [
      {
        "id": 789,
        "video": {
          "id": 123,
          "title": "Never Gonna Give You Up",
          "thumbnail_url": "https://img.youtube.com/vi/dQw4w9WgXcQ/maxresdefault.jpg",
          "duration": 212.0
        },
        "last_position": 67.5,
        "total_watch_time": 45.2,
        "notes_count": 3,
        "ai_queries_count": 5,
        "last_accessed": "2024-01-15T10:45:00Z"
      }
    ]
  }
}
```

## Voice Interaction Endpoints

### POST /voice/transcribe

**Description**: Transcribe audio to text

**Request Body** (multipart/form-data):

- `audio`: Audio file
- `language`: Language code (optional)
- `context`: Video context for better accuracy (optional)

**Response**:

```json
{
  "success": true,
  "data": {
    "transcription": "Skip to the part about machine learning",
    "confidence_score": 0.96,
    "language": "en",
    "processing_time": 0.8,
    "detected_intent": "navigation_command"
  }
}
```

### POST /voice/synthesize

**Description**: Convert text to speech

**Request Body**:

```json
{
  "text": "The main theme of this song is unwavering commitment",
  "voice": "default",
  "language": "en",
  "speed": 1.0
}
```

**Response**:

```json
{
  "success": true,
  "data": {
    "audio_url": "https://storage.example.com/tts/response_123.mp3",
    "duration": 3.2,
    "format": "mp3"
  }
}
```

## Analytics Endpoints

### GET /analytics/user

**Description**: Get user analytics and learning insights

**Query Parameters**:

- `period`: `day`, `week`, `month`, `year`
- `start_date`: Start date (ISO format)
- `end_date`: End date (ISO format)

**Response**:

```json
{
  "success": true,
  "data": {
    "analytics": {
      "period": "week",
      "videos_watched": 12,
      "total_watch_time": 3600.0,
      "notes_created": 25,
      "ai_interactions": 48,
      "voice_interactions": 15,
      "most_used_features": ["ai_qa", "note_taking", "voice_commands"],
      "learning_progress": {
        "topics_explored": ["music", "technology", "science"],
        "comprehension_score": 0.85,
        "engagement_level": "high"
      }
    }
  }
}
```

## WebSocket Events

### Connection

```javascript
// Connect to WebSocket
const ws = new WebSocket('wss://api.videointeraction.com/ws/video/123?token=jwt_token');
```

### Events

#### video_time_update

```json
{
  "event": "video_time_update",
  "data": {
    "current_time": 67.5,
    "active_segment": {
      "id": 15,
      "text": "We're no strangers to love",
      "start_time": 65.0,
      "end_time": 68.5
    }
  }
}
```

#### transcript_highlight

```json
{
  "event": "transcript_highlight",
  "data": {
    "segment_id": 15,
    "highlight_type": "active"
  }
}
```

#### note_created

```json
{
  "event": "note_created",
  "data": {
    "note": {
      "id": 456,
      "timestamp": 67.5,
      "content": "Important insight here"
    }
  }
}
```

#### ai_response_stream

```json
{
  "event": "ai_response_stream",
  "data": {
    "interaction_id": 789,
    "partial_response": "The main theme of this song is...",
    "is_complete": false
  }
}
```

This comprehensive API specification provides a complete interface for all platform features, ensuring consistent, well-documented, and developer-friendly access to the universal video interaction platform.
