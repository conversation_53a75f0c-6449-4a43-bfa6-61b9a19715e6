# InsightStream - Environment Configuration
# Unlock the Knowledge Within Video

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Neon PostgreSQL Database URL with asyncpg driver
# Get your connection string from: https://neon.tech
DATABASE_URL=postgresql+asyncpg://username:<EMAIL>/insightstream_db?sslmode=require

# Database connection pool settings
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
# Redis URL for caching and session management
REDIS_URL=redis://localhost:6379/0

# Redis connection settings
REDIS_MAX_CONNECTIONS=50
REDIS_RETRY_ON_TIMEOUT=true
REDIS_SOCKET_KEEPALIVE=true

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Application environment (development, staging, production)
ENVIRONMENT=development

# Secret key for JWT tokens and encryption
SECRET_KEY=your-super-secret-key-change-this-in-production

# JWT token settings
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# API Configuration
API_V1_PREFIX=/api/v1
API_TITLE=InsightStream
API_VERSION=1.0.0
API_DESCRIPTION=Unlock the Knowledge Within Video - Revolutionary AI-powered video interaction platform

# CORS settings
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
CORS_ALLOW_CREDENTIALS=true
CORS_ALLOW_METHODS=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
CORS_ALLOW_HEADERS=["*"]

# =============================================================================
# AI SERVICES CONFIGURATION
# =============================================================================
# Agno.com AI Agents Configuration
AGNO_API_KEY=your-agno-api-key-here
AGNO_BASE_URL=https://api.agno.com
AGNO_MODEL=claude-3-5-sonnet-latest
AGNO_MAX_TOKENS=4000
AGNO_TEMPERATURE=0.7

# Groq Configuration (Primary LLM Provider - Free Tier)
GROQ_API_KEY=your-groq-api-key-here
GROQ_MODEL=llama3-8b-8192
GROQ_MAX_TOKENS=4000
GROQ_TEMPERATURE=0.7

# OpenAI Configuration (for Whisper transcription only)
OPENAI_API_KEY=your-openai-api-key-here
WHISPER_MODEL=whisper-1

# LLM Provider Configuration
PRIMARY_LLM_PROVIDER=groq
FALLBACK_LLM_PROVIDER=agno

# Voice Configuration (Browser-based - 100% Free)
VOICE_ENABLED=true
VOICE_PROVIDER=browser  # Uses Web Speech API (no API key required)

# Text-to-Speech Settings (Browser SpeechSynthesis API)
TTS_ENABLED=true
TTS_VOICE_NAME=default  # Browser will use system default voice
TTS_RATE=1.0           # Speech rate (0.1 to 10)
TTS_PITCH=1.0          # Voice pitch (0 to 2)
TTS_VOLUME=1.0         # Volume (0 to 1)
TTS_LANGUAGE=en-US     # Language code

# Speech-to-Text Settings (Browser SpeechRecognition API)
STT_ENABLED=true
STT_LANGUAGE=en-US
STT_CONTINUOUS=false    # Stop after each pause
STT_INTERIM_RESULTS=true # Show results while speaking
STT_MAX_ALTERNATIVES=1

# Optional: ElevenLabs (for premium users who want higher quality)
ELEVENLABS_ENABLED=false
ELEVENLABS_API_KEY=optional-elevenlabs-api-key
ELEVENLABS_VOICE_ID=optional-voice-id

# Pydantic AI Configuration
PYDANTIC_AI_MODEL=claude-3-5-sonnet
PYDANTIC_AI_MAX_RETRIES=3
PYDANTIC_AI_TIMEOUT=30

# =============================================================================
# VIDEO PROCESSING CONFIGURATION
# =============================================================================
# yt-dlp Configuration (Modern replacement for youtube-dl)
YTDLP_EXTRACT_FLAT=false
YTDLP_NO_WARNINGS=true
YTDLP_QUIET=true
YTDLP_FORMAT=best[height<=720]
YTDLP_WRITESUBTITLES=true
YTDLP_WRITEAUTOMATICSUB=true
YTDLP_SUBTITLESLANGS=en,en-US
YTDLP_IGNOREERRORS=true

# YouTube Transcript API Configuration
YOUTUBE_TRANSCRIPT_LANGUAGES=["en", "en-US", "en-GB"]
YOUTUBE_TRANSCRIPT_PRESERVE_FORMATTING=false
YOUTUBE_TRANSCRIPT_PROXIES=null

# Video Processing Settings
MAX_VIDEO_DURATION=7200  # 2 hours in seconds
MAX_FILE_SIZE=500MB
SUPPORTED_FORMATS=["mp4", "webm", "mkv", "avi", "mov"]

# Transcript Processing
TRANSCRIPT_CONFIDENCE_THRESHOLD=0.7
TRANSCRIPT_MAX_SEGMENT_LENGTH=1000
TRANSCRIPT_LANGUAGE_DETECTION=true

# =============================================================================
# PLATFORM API KEYS
# =============================================================================
# YouTube Data API
YOUTUBE_API_KEY=your-youtube-api-key-here
YOUTUBE_API_VERSION=v3

# TikTok API (when available)
TIKTOK_API_KEY=your-tiktok-api-key-here
TIKTOK_API_SECRET=your-tiktok-api-secret-here

# LinkedIn API
LINKEDIN_CLIENT_ID=your-linkedin-client-id-here
LINKEDIN_CLIENT_SECRET=your-linkedin-client-secret-here

# Facebook/Meta API
FACEBOOK_APP_ID=your-facebook-app-id-here
FACEBOOK_APP_SECRET=your-facebook-app-secret-here

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================
# Qdrant Vector Database
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your-qdrant-api-key-here
QDRANT_COLLECTION_NAME=video_embeddings
QDRANT_VECTOR_SIZE=384
QDRANT_DISTANCE_METRIC=cosine

# Embedding Configuration
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_PROVIDER=huggingface  # Free alternative to OpenAI embeddings
EMBEDDING_BATCH_SIZE=100
EMBEDDING_MAX_TOKENS=512
EMBEDDING_DIMENSION=384

# =============================================================================
# KNOWLEDGE GRAPH CONFIGURATION
# =============================================================================
# Knowledge Graph Settings
KNOWLEDGE_GRAPH_ENABLED=true
KNOWLEDGE_GRAPH_AUTO_GENERATE=true
KNOWLEDGE_GRAPH_MAX_NODES=1000
KNOWLEDGE_GRAPH_MAX_RELATIONSHIPS=2000

# Graph Database (Neo4j or in-memory)
GRAPH_DATABASE_TYPE=neo4j  # Options: neo4j, memory, networkx
NEO4J_URI=bolt://localhost:7687
NEO4J_USERNAME=neo4j
NEO4J_PASSWORD=your-neo4j-password

# Knowledge Extraction
KNOWLEDGE_EXTRACTION_MODEL=llama3-8b-8192
KNOWLEDGE_EXTRACTION_PROVIDER=groq
KNOWLEDGE_EXTRACTION_CONFIDENCE_THRESHOLD=0.8
ENTITY_RECOGNITION_MODEL=spacy_en_core_web_sm
RELATIONSHIP_EXTRACTION_MODEL=llama3-8b-8192
RELATIONSHIP_EXTRACTION_PROVIDER=groq

# =============================================================================
# FILE STORAGE CONFIGURATION
# =============================================================================
# AWS S3 Compatible Storage
AWS_ACCESS_KEY_ID=your-aws-access-key-id
AWS_SECRET_ACCESS_KEY=your-aws-secret-access-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=insightstream-storage
S3_ENDPOINT_URL=https://s3.amazonaws.com

# Local Storage (for development)
LOCAL_STORAGE_PATH=./uploads
LOCAL_STORAGE_MAX_SIZE=10GB

# File Upload Settings
MAX_UPLOAD_SIZE=100MB
ALLOWED_AUDIO_FORMATS=["mp3", "wav", "m4a", "ogg", "flac"]
ALLOWED_IMAGE_FORMATS=["jpg", "jpeg", "png", "gif", "webp"]
ALLOWED_VIDEO_FORMATS=["mp4", "webm", "mkv", "avi", "mov"]

# =============================================================================
# LOGGING AND MONITORING
# =============================================================================
# Logfire Configuration
LOGFIRE_TOKEN=your-logfire-token-here
LOGFIRE_PROJECT_NAME=insightstream
LOGFIRE_ENVIRONMENT=development
LOGFIRE_LOG_LEVEL=INFO

# Rich Terminal Configuration
RICH_CONSOLE_WIDTH=120
RICH_CONSOLE_COLOR_SYSTEM=auto
RICH_CONSOLE_FORCE_TERMINAL=false

# Sentry Error Tracking
SENTRY_DSN=your-sentry-dsn-here
SENTRY_ENVIRONMENT=development
SENTRY_TRACES_SAMPLE_RATE=0.1
SENTRY_PROFILES_SAMPLE_RATE=0.1

# Prometheus Metrics
PROMETHEUS_ENABLED=true
PROMETHEUS_PORT=8001
PROMETHEUS_METRICS_PATH=/metrics

# =============================================================================
# BACKGROUND TASK CONFIGURATION
# =============================================================================
# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=["json"]
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# Task Queue Settings
CELERY_TASK_ROUTES={
    "video_processing.*": {"queue": "video_processing"},
    "ai_processing.*": {"queue": "ai_processing"},
    "transcript_processing.*": {"queue": "transcript_processing"}
}

# Worker Configuration
CELERY_WORKER_CONCURRENCY=4
CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
CELERY_WORKER_PREFETCH_MULTIPLIER=1

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
RATE_LIMIT_BURST_SIZE=20

# Security Headers
SECURITY_HEADERS_ENABLED=true
HSTS_MAX_AGE=31536000
CONTENT_SECURITY_POLICY=default-src 'self'

# Password Security
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=true

# Session Security
SESSION_COOKIE_SECURE=true
SESSION_COOKIE_HTTPONLY=true
SESSION_COOKIE_SAMESITE=strict

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
# Caching Settings
CACHE_TTL_DEFAULT=3600  # 1 hour
CACHE_TTL_TRANSCRIPT=86400  # 24 hours
CACHE_TTL_VIDEO_METADATA=43200  # 12 hours
CACHE_TTL_AI_RESPONSES=7200  # 2 hours

# Connection Timeouts
HTTP_TIMEOUT=30
DATABASE_TIMEOUT=30
REDIS_TIMEOUT=5
AI_API_TIMEOUT=60

# Pagination Settings
DEFAULT_PAGE_SIZE=20
MAX_PAGE_SIZE=100

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
# Debug Settings (only for development)
DEBUG=true
DEBUG_SQL=false
DEBUG_TOOLBAR=true

# Hot Reload Settings
RELOAD=true
RELOAD_DIRS=["app"]

# Testing Configuration
TEST_DATABASE_URL=postgresql+asyncpg://test_user:test_pass@localhost:5432/test_insightstream
TEST_REDIS_URL=redis://localhost:6379/15

# =============================================================================
# FEATURE FLAGS
# =============================================================================
# Feature toggles for gradual rollout
FEATURE_VOICE_INTERACTION=true
FEATURE_MULTI_PLATFORM=true
FEATURE_AI_NOTE_ASSISTANCE=true
FEATURE_CROSS_VIDEO_CONNECTIONS=true
FEATURE_ANALYTICS_DASHBOARD=true
FEATURE_COLLABORATIVE_NOTES=false
FEATURE_LIVE_STREAMING=false

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_TLS=true
SMTP_SSL=false

# Webhook Configuration
WEBHOOK_SECRET=your-webhook-secret-here
WEBHOOK_TIMEOUT=10

# =============================================================================
# ANALYTICS CONFIGURATION
# =============================================================================
# User Analytics
ANALYTICS_ENABLED=true
ANALYTICS_RETENTION_DAYS=365
ANALYTICS_BATCH_SIZE=1000

# Performance Monitoring
PERFORMANCE_MONITORING=true
SLOW_QUERY_THRESHOLD=1.0  # seconds
MEMORY_USAGE_THRESHOLD=80  # percentage

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
# Database Backup
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_S3_BUCKET=insightstream-backups

# File Backup
FILE_BACKUP_ENABLED=true
FILE_BACKUP_SCHEDULE=0 3 * * 0  # Weekly on Sunday at 3 AM
