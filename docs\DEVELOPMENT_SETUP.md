# InsightStream Development Setup Guide

## 🚀 Prerequisites

### Required Software

- **Python 3.11+** for FastAPI backend development
- **uv** - Modern Python package manager (install from: <https://docs.astral.sh/uv/>)
- **Node.js 18+** for React frontend development
- **Git** for version control

### External Services

- **Neon PostgreSQL** - Cloud PostgreSQL database (get connection string from neon.tech)
- **Redis Cloud** - For caching and background tasks (or local Redis installation)

### Development Tools (Recommended)

- **VS Code** with Python and TypeScript extensions
- **Postman** or **Insomnia** for API testing
- **Neon Console** for database management
- **Redis Insight** for Redis management

## 📁 Project Structure

```plaintext
insightstream/
├── README.md
├── .env.example                    # Environment variables template
├── .gitignore
├── docker-compose.yml             # Development environment
├── docker-compose.prod.yml        # Production environment
├── Makefile                       # Development commands
├──
├── backend/                       # FastAPI Backend
│   ├── app/
│   │   ├── __init__.py
│   │   ├── main.py               # FastAPI application entry point
│   │   ├──
│   │   ├── core/                 # Core configuration and utilities
│   │   │   ├── __init__.py
│   │   │   ├── config.py         # Application configuration
│   │   │   ├── database.py       # Database connection and session management
│   │   │   ├── security.py       # Authentication and authorization
│   │   │   ├── celery.py         # Celery configuration
│   │   │   ├── logging.py        # Logfire and Rich logging setup
│   │   │   └── exceptions.py     # Custom exception handlers
│   │   │
│   │   ├── models/               # SQLModel database models
│   │   │   ├── __init__.py
│   │   │   ├── base.py           # Base model with common fields
│   │   │   ├── user.py           # User and authentication models
│   │   │   ├── video.py          # Video and transcript models
│   │   │   ├── note.py           # Note-taking models
│   │   │   ├── ai_interaction.py # AI interaction models
│   │   │   ├── session.py        # User session models
│   │   │   └── analytics.py      # Analytics and metrics models
│   │   │
│   │   ├── schemas/              # Pydantic request/response schemas
│   │   │   ├── __init__.py
│   │   │   ├── user.py           # User-related schemas
│   │   │   ├── video.py          # Video processing schemas
│   │   │   ├── ai.py             # AI interaction schemas
│   │   │   ├── note.py           # Note-taking schemas
│   │   │   ├── session.py        # Session management schemas
│   │   │   └── common.py         # Common response schemas
│   │   │
│   │   ├── api/                  # API endpoints
│   │   │   ├── __init__.py
│   │   │   ├── deps.py           # Dependency injection
│   │   │   └── v1/               # API version 1
│   │   │       ├── __init__.py
│   │   │       ├── auth.py       # Authentication endpoints
│   │   │       ├── videos.py     # Video processing endpoints
│   │   │       ├── ai.py         # AI interaction endpoints
│   │   │       ├── notes.py      # Note-taking endpoints
│   │   │       ├── sessions.py   # Session management endpoints
│   │   │       ├── voice.py      # Voice interaction endpoints
│   │   │       ├── analytics.py  # Analytics endpoints
│   │   │       └── websocket.py  # WebSocket endpoints
│   │   │
│   │   ├── services/             # Business logic services
│   │   │   ├── __init__.py
│   │   │   ├── video_processor.py    # Video extraction and processing
│   │   │   ├── transcript_service.py # Transcript extraction and enhancement
│   │   │   ├── ai_orchestrator.py    # Multi-agent AI coordination
│   │   │   ├── ai_agents/            # Specialized AI agents
│   │   │   │   ├── __init__.py
│   │   │   │   ├── base_agent.py     # Base agent class
│   │   │   │   ├── video_analysis_agent.py
│   │   │   │   ├── qa_agent.py
│   │   │   │   ├── note_assistant_agent.py
│   │   │   │   ├── transcript_agent.py
│   │   │   │   ├── speech_processor_agent.py
│   │   │   │   └── content_discovery_agent.py
│   │   │   ├── note_service.py       # Note creation and management
│   │   │   ├── session_service.py    # Session management
│   │   │   ├── voice_service.py      # Speech-to-text and text-to-speech
│   │   │   ├── search_service.py     # Vector search and semantic queries
│   │   │   ├── analytics_service.py  # User analytics and insights
│   │   │   └── knowledge_graph/      # Knowledge graph services
│   │   │       ├── __init__.py
│   │   │       ├── graph_service.py  # Main knowledge graph service
│   │   │       ├── entity_extractor.py # Entity recognition and extraction
│   │   │       ├── relationship_mapper.py # Relationship identification
│   │   │       ├── graph_generator.py # Automatic graph generation
│   │   │       └── graph_visualizer.py # Graph visualization data
│   │   │
│   │   ├── utils/                # Utility functions
│   │   │   ├── __init__.py
│   │   │   ├── helpers.py        # General utility functions
│   │   │   ├── validators.py     # Data validation utilities
│   │   │   ├── formatters.py     # Data formatting utilities
│   │   │   └── constants.py      # Application constants
│   │   │
│   │   └── workers/              # Background task workers
│   │       ├── __init__.py
│   │       ├── video_tasks.py    # Video processing tasks
│   │       ├── ai_tasks.py       # AI processing tasks
│   │       ├── transcript_tasks.py # Transcript processing tasks
│   │       └── analytics_tasks.py  # Analytics processing tasks
│   │
│   ├── alembic/                  # Database migrations
│   │   ├── versions/
│   │   ├── env.py
│   │   ├── script.py.mako
│   │   └── alembic.ini
│   │
│   ├── tests/                    # Comprehensive test suite
│   │   ├── __init__.py
│   │   ├── conftest.py           # Test configuration and fixtures
│   │   ├── unit/                 # Unit tests
│   │   │   ├── test_models.py
│   │   │   ├── test_services.py
│   │   │   ├── test_ai_agents.py
│   │   │   └── test_utils.py
│   │   ├── integration/          # Integration tests
│   │   │   ├── test_api_endpoints.py
│   │   │   ├── test_database.py
│   │   │   ├── test_ai_integration.py
│   │   │   └── test_video_processing.py
│   │   └── e2e/                  # End-to-end tests
│   │       ├── test_user_journey.py
│   │       ├── test_video_workflow.py
│   │       └── test_ai_interactions.py
│   │
│   ├── scripts/                  # Utility scripts
│   │   ├── init_db.py           # Database initialization
│   │   ├── seed_data.py         # Test data seeding
│   │   └── migrate.py           # Migration utilities
│   │
│   ├── requirements.txt          # Production dependencies
│   ├── requirements-dev.txt      # Development dependencies
│   ├── Dockerfile               # Production Docker image
│   ├── Dockerfile.dev           # Development Docker image
│   └── pyproject.toml           # Python project configuration
│
├── frontend/                     # React Frontend
│   ├── public/
│   │   ├── index.html
│   │   ├── favicon.ico
│   │   ├── manifest.json
│   │   └── robots.txt
│   │
│   ├── src/
│   │   ├── main.tsx             # Application entry point
│   │   ├── App.tsx              # Main application component
│   │   ├── index.css            # Global styles
│   │   ├── vite-env.d.ts        # Vite type definitions
│   │   │
│   │   ├── components/          # React components
│   │   │   ├── common/          # Reusable UI components
│   │   │   │   ├── Button/
│   │   │   │   ├── Input/
│   │   │   │   ├── Modal/
│   │   │   │   ├── Loading/
│   │   │   │   └── ErrorBoundary/
│   │   │   │
│   │   │   ├── VideoPlayer/     # Custom video player
│   │   │   │   ├── VideoPlayer.tsx
│   │   │   │   ├── VideoControls.tsx
│   │   │   │   ├── ProgressBar.tsx
│   │   │   │   └── VideoPlayer.module.css
│   │   │   │
│   │   │   ├── Transcript/      # Interactive transcript
│   │   │   │   ├── TranscriptViewer.tsx
│   │   │   │   ├── TranscriptSegment.tsx
│   │   │   │   ├── TranscriptSearch.tsx
│   │   │   │   └── TranscriptHighlight.tsx
│   │   │   │
│   │   │   ├── AIChat/          # AI interaction interface
│   │   │   │   ├── ChatInterface.tsx
│   │   │   │   ├── MessageBubble.tsx
│   │   │   │   ├── QuestionSuggestions.tsx
│   │   │   │   └── TypingIndicator.tsx
│   │   │   │
│   │   │   ├── Notes/           # Note-taking components
│   │   │   │   ├── NoteEditor.tsx
│   │   │   │   ├── NotesList.tsx
│   │   │   │   ├── NoteCard.tsx
│   │   │   │   ├── MediaAttachment.tsx
│   │   │   │   └── VoiceNote.tsx
│   │   │   │
│   │   │   ├── Voice/           # Voice interaction
│   │   │   │   ├── VoiceRecorder.tsx
│   │   │   │   ├── VoiceCommands.tsx
│   │   │   │   └── SpeechVisualizer.tsx
│   │   │   │
│   │   │   ├── Session/         # Session management
│   │   │   │   ├── SessionHistory.tsx
│   │   │   │   ├── SessionCard.tsx
│   │   │   │   └── SessionRestore.tsx
│   │   │   │
│   │   │   ├── KnowledgeGraph/  # Knowledge graph visualization
│   │   │   │   ├── GraphViewer.tsx
│   │   │   │   ├── GraphNode.tsx
│   │   │   │   ├── GraphEdge.tsx
│   │   │   │   ├── GraphControls.tsx
│   │   │   │   ├── GraphLegend.tsx
│   │   │   │   └── GraphExport.tsx
│   │   │   │
│   │   │   └── Layout/          # Layout components
│   │   │       ├── Header.tsx
│   │   │       ├── Sidebar.tsx
│   │   │       ├── MainLayout.tsx
│   │   │       └── Navigation.tsx
│   │   │
│   │   ├── hooks/               # Custom React hooks
│   │   │   ├── useVideoPlayer.ts
│   │   │   ├── useTranscript.ts
│   │   │   ├── useAIChat.ts
│   │   │   ├── useNotes.ts
│   │   │   ├── useVoice.ts
│   │   │   ├── useWebSocket.ts
│   │   │   └── useLocalStorage.ts
│   │   │
│   │   ├── services/            # API client and data fetching
│   │   │   ├── api.ts           # Base API client
│   │   │   ├── auth.ts          # Authentication service
│   │   │   ├── videos.ts        # Video-related API calls
│   │   │   ├── ai.ts            # AI interaction API calls
│   │   │   ├── notes.ts         # Notes API calls
│   │   │   ├── voice.ts         # Voice API calls
│   │   │   └── websocket.ts     # WebSocket service
│   │   │
│   │   ├── stores/              # Zustand state management
│   │   │   ├── authStore.ts     # Authentication state
│   │   │   ├── videoStore.ts    # Video player state
│   │   │   ├── transcriptStore.ts # Transcript state
│   │   │   ├── aiStore.ts       # AI interaction state
│   │   │   ├── notesStore.ts    # Notes state
│   │   │   ├── voiceStore.ts    # Voice interaction state
│   │   │   └── sessionStore.ts  # Session state
│   │   │
│   │   ├── types/               # TypeScript type definitions
│   │   │   ├── api.ts           # API response types
│   │   │   ├── video.ts         # Video-related types
│   │   │   ├── ai.ts            # AI interaction types
│   │   │   ├── note.ts          # Note-taking types
│   │   │   ├── user.ts          # User-related types
│   │   │   └── common.ts        # Common types
│   │   │
│   │   ├── utils/               # Utility functions
│   │   │   ├── formatters.ts    # Data formatting utilities
│   │   │   ├── validators.ts    # Form validation utilities
│   │   │   ├── constants.ts     # Application constants
│   │   │   ├── helpers.ts       # General helper functions
│   │   │   └── storage.ts       # Local storage utilities
│   │   │
│   │   └── styles/              # Styling
│   │       ├── globals.css      # Global CSS styles
│   │       ├── components.css   # Component-specific styles
│   │       └── themes.css       # Theme definitions
│   │
│   ├── tests/                   # Frontend tests
│   │   ├── setup.ts             # Test setup configuration
│   │   ├── __mocks__/           # Mock implementations
│   │   ├── components/          # Component tests
│   │   ├── hooks/               # Hook tests
│   │   ├── services/            # Service tests
│   │   ├── stores/              # Store tests
│   │   └── e2e/                 # End-to-end tests
│   │       ├── video-interaction.spec.ts
│   │       ├── ai-chat.spec.ts
│   │       └── note-taking.spec.ts
│   │
│   ├── package.json             # Node.js dependencies and scripts
│   ├── package-lock.json        # Dependency lock file
│   ├── tsconfig.json            # TypeScript configuration
│   ├── tsconfig.node.json       # Node.js TypeScript configuration
│   ├── vite.config.ts           # Vite build configuration
│   ├── tailwind.config.js       # Tailwind CSS configuration
│   ├── postcss.config.js        # PostCSS configuration
│   ├── eslint.config.js         # ESLint configuration
│   ├── prettier.config.js       # Prettier configuration
│   ├── playwright.config.ts     # Playwright E2E test configuration
│   ├── jest.config.js           # Jest test configuration
│   ├── Dockerfile               # Production Docker image
│   └── Dockerfile.dev           # Development Docker image
│
├── docs/                        # Project documentation
│   ├── PROJECT_PLAN.md
│   ├── INSIGHTSTREAM_OVERVIEW.md
│   ├── SYSTEM_DIAGRAMS.md
│   ├── API_SPECIFICATIONS.md
│   ├── ENHANCED_IMPLEMENTATION_ROADMAP.md
│   ├── INNOVATION_FEATURES.md
│   ├── COMPREHENSIVE_TESTING_STRATEGY.md
│   ├── PRODUCTION_DEPLOYMENT_GUIDE.md
│   └── PROJECT_SUMMARY.md
│
├── k8s/                         # Kubernetes deployment files
│   ├── namespace.yaml
│   ├── configmap.yaml
│   ├── secrets.yaml
│   ├── backend-deployment.yaml
│   ├── frontend-deployment.yaml
│   ├── celery-deployment.yaml
│   ├── postgres.yaml
│   ├── redis.yaml
│   ├── ingress.yaml
│   └── monitoring/
│       ├── prometheus.yaml
│       ├── grafana.yaml
│       └── alertmanager.yaml
│
├── scripts/                     # Development and deployment scripts
│   ├── setup.sh                # Initial project setup
│   ├── dev-start.sh            # Start development environment
│   ├── test.sh                 # Run all tests
│   ├── build.sh                # Build production images
│   ├── deploy.sh               # Deploy to production
│   └── backup.sh               # Database backup script
│
└── .github/                    # GitHub Actions workflows
    └── workflows/
        ├── ci.yml              # Continuous integration
        ├── cd.yml              # Continuous deployment
        ├── test.yml            # Automated testing
        └── security.yml        # Security scanning
```

## 🚀 Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd insightstream

# Create environment files
cp .env.example .env
cp frontend/.env.example frontend/.env.local

# Make scripts executable
chmod +x scripts/*.sh
```

### 2. Environment Configuration

Edit the `.env` file with your specific configuration:

```bash
# Database Configuration (PostgreSQL with asyncpg)
DATABASE_URL=postgresql+asyncpg://insightstream_user:your_password@localhost:5432/insightstream_db

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Application Settings
SECRET_KEY=your-super-secret-key-change-this-in-production
ENVIRONMENT=development

# AI Service API Keys
AGNO_API_KEY=your-agno-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here

# Logging and Monitoring
LOGFIRE_TOKEN=your-logfire-token-here
SENTRY_DSN=your-sentry-dsn-here

# File Storage
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
S3_BUCKET_NAME=insightstream-storage
```

Edit the `frontend/.env.local` file:

```bash
# Frontend Configuration
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_WS_URL=ws://localhost:8000/ws
VITE_ENVIRONMENT=development
VITE_APP_NAME=InsightStream
VITE_APP_MOTTO="Unlock the Knowledge Within Video"
```

### 3. Development Setup

#### Backend Setup

```bash
# Navigate to backend directory
cd backend

# Initialize uv project (creates virtual environment automatically)
uv init --python 3.11

# Install dependencies using uv
uv add fastapi uvicorn sqlmodel alembic asyncpg redis celery logfire rich agno pydantic-ai groq
uv add --dev pytest pytest-cov pytest-asyncio ruff mypy pre-commit

# Set up database
uv run python scripts/init_db.py

# Run database migrations
uv run alembic upgrade head

# Start the development server
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Setup

```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Install free speech packages (no API keys required)
npm install react-speakup react-speech-recognition

# Start development server
npm run dev

# The frontend will be available at http://localhost:3000
```

#### Background Workers

```bash
# In a separate terminal, start Celery workers
cd backend
celery -A app.core.celery worker --loglevel=info

# In another terminal, start Celery beat (scheduler)
celery -A app.core.celery beat --loglevel=info
```

## 🛠 Development Commands

### Using Make Commands

```bash
# Setup development environment
make setup

# Start development servers
make dev

# Run all tests
make test

# Run backend tests only
make test-backend

# Run frontend tests only
make test-frontend

# Run linting and formatting
make lint

# Build production images
make build

# Clean up development environment
make clean
```

### Backend Commands

```bash
# Database operations
alembic revision --autogenerate -m "Description of changes"
alembic upgrade head
alembic downgrade -1

# Testing
uv run pytest                              # Run all tests
uv run pytest --cov=app                   # Run tests with coverage
uv run pytest tests/unit/                 # Run unit tests only
uv run pytest tests/integration/          # Run integration tests only

# Code quality
uv run ruff format .                       # Format code with ruff
uv run ruff check .                        # Lint code with ruff
uv run ruff check --fix .                  # Auto-fix linting issues
uv run mypy app                           # Type checking

# Development server
uv run uvicorn app.main:app --reload      # Start with hot reload
uv run uvicorn app.main:app --reload --port 8001  # Start on different port
```

### Frontend Commands

```bash
# Development
npm run dev                        # Start development server
npm run dev -- --port 3001        # Start on different port

# Testing
npm test                           # Run unit tests
npm run test:watch                 # Run tests in watch mode
npm run test:coverage              # Run tests with coverage
npm run e2e                        # Run end-to-end tests
npm run e2e:ui                     # Run E2E tests with UI

# Code quality
npm run lint                       # Lint code
npm run lint:fix                   # Fix linting issues
npm run type-check                 # TypeScript type checking

# Build
npm run build                      # Build for production
npm run preview                    # Preview production build
```

## 🔧 Development Tools Configuration

### VS Code Settings

Create `.vscode/settings.json`:

```json
{
  "python.defaultInterpreterPath": "./backend/.venv/bin/python",
  "python.linting.enabled": false,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true,
    "source.fixAll.ruff": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "eslint.workingDirectories": ["frontend"],
  "tailwindCSS.includeLanguages": {
    "typescript": "javascript",
    "typescriptreact": "javascript"
  },
  "[python]": {
    "editor.defaultFormatter": "charliermarsh.ruff",
    "editor.codeActionsOnSave": {
      "source.organizeImports.ruff": true,
      "source.fixAll.ruff": true
    }
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescriptreact]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "ruff.enable": true,
  "ruff.organizeImports": true,
  "mypy-type-checker.importStrategy": "useBundled"
}
```

### VS Code Extensions

Install these recommended extensions:

```json
{
  "recommendations": [
    "ms-python.python",
    "charliermarsh.ruff",
    "ms-python.mypy-type-checker",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml"
  ]
}
```

## 🐛 Debugging Configuration

### Backend Debugging (VS Code)

Create `.vscode/launch.json`:

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Debug FastAPI",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/.venv/bin/python",
      "args": ["-m", "uvicorn", "app.main:app", "--reload", "--host", "0.0.0.0", "--port", "8000"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      },
      "console": "integratedTerminal"
    },
    {
      "name": "Debug Tests",
      "type": "python",
      "request": "launch",
      "module": "pytest",
      "args": ["tests/", "-v"],
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      },
      "console": "integratedTerminal"
    }
  ]
}
```

### Frontend Debugging

The Vite development server includes built-in debugging support. Use browser developer tools for debugging React components.

## 📊 Monitoring and Logging

### Logfire Integration

InsightStream uses Logfire for comprehensive logging and monitoring:

```python
# Example usage in backend code
from app.core.logging import logger

# Structured logging with context
logger.info(
    "Video processing started",
    extra={
        "video_id": video.id,
        "platform": video.platform,
        "duration": video.duration
    }
)

# Error logging with stack traces
try:
    result = await process_video(video_url)
except Exception as e:
    logger.error(
        "Video processing failed",
        extra={
            "video_url": video_url,
            "error": str(e)
        },
        exc_info=True
    )
```

### Rich Terminal Output

The backend uses Rich library for beautiful terminal output:

```python
from rich.console import Console
from rich.table import Table
from rich.progress import Progress

console = Console()

# Beautiful tables
table = Table(title="Video Processing Status")
table.add_column("Video ID", style="cyan")
table.add_column("Status", style="green")
table.add_column("Progress", style="yellow")

# Progress bars
with Progress() as progress:
    task = progress.add_task("Processing video...", total=100)
    # Update progress as needed
    progress.update(task, advance=10)
```

## 🧪 Testing Strategy

### Running Tests

```bash
# Backend tests
cd backend
pytest                                    # All tests
pytest tests/unit/                       # Unit tests
pytest tests/integration/                # Integration tests
pytest tests/e2e/                        # End-to-end tests
pytest --cov=app --cov-report=html      # Coverage report

# Frontend tests
cd frontend
npm test                                 # Unit tests
npm run test:coverage                    # Coverage report
npm run e2e                             # End-to-end tests
```

### Test Data Management

```bash
# Seed test data
cd backend
python scripts/seed_data.py

# Reset test database
python scripts/init_db.py --reset
```

## 🚀 Production Deployment

### Building Production Images

```bash
# Build backend image
docker build -f backend/Dockerfile -t insightstream/api:latest backend/

# Build frontend image
docker build -f frontend/Dockerfile -t insightstream/frontend:latest frontend/

# Build all images
make build
```

### Environment-Specific Configurations

- **Development**: Use `docker-compose.yml`
- **Staging**: Use `docker-compose.staging.yml`
- **Production**: Use Kubernetes configurations in `k8s/`

## 🔒 Security Considerations

### Development Security

- Never commit real API keys or secrets
- Use `.env.example` as template, not actual `.env`
- Regularly update dependencies
- Use HTTPS in production environments
- Implement proper CORS settings

### Database Security

- Use strong passwords for database connections
- Enable SSL/TLS for database connections in production
- Regularly backup database
- Implement proper access controls

## 📚 Additional Resources

### Documentation

- [FastAPI Documentation](https://fastapi.tiangolo.com/)
- [React Documentation](https://react.dev/)
- [SQLModel Documentation](https://sqlmodel.tiangolo.com/)
- [Alembic Documentation](https://alembic.sqlalchemy.org/)
- [Logfire Documentation](https://logfire.pydantic.dev/)

### Learning Resources

- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)
- [Docker Best Practices](https://docs.docker.com/develop/best-practices/)
- [Kubernetes Documentation](https://kubernetes.io/docs/)

This comprehensive development setup guide provides everything needed to get InsightStream running locally and prepare for production deployment. The modular architecture and comprehensive tooling ensure a smooth development experience while maintaining high code quality standards.
