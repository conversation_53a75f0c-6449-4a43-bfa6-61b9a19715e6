"""
Custom Exception Classes and Handlers

This module defines custom exceptions and their handlers for the InsightStream
backend, providing consistent error responses and logging.
"""

import logging
from typing import Any

import logfire
from fastapi import HTTPException, Request, status
from fastapi.responses import JSONResponse
from pydantic import ValidationError

logger = logging.getLogger(__name__)


class InsightStreamException(Exception):
    """Base exception class for InsightStream application."""

    def __init__(
        self,
        message: str,
        status_code: int = status.HTTP_500_INTERNAL_SERVER_ERROR,
        details: dict[str, Any] | None = None,
        error_code: str | None = None,
    ):
        self.message = message
        self.status_code = status_code
        self.details = details or {}
        self.error_code = error_code or self.__class__.__name__
        super().__init__(self.message)


class ValidationException(InsightStreamException):
    """Exception raised for validation errors."""

    def __init__(self, message: str, field: str | None = None, value: Any = None):
        details = {}
        if field:
            details["field"] = field
        if value is not None:
            details["value"] = str(value)

        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
            error_code="VALIDATION_ERROR",
        )


class AuthenticationException(InsightStreamException):
    """Exception raised for authentication errors."""

    def __init__(self, message: str = "Authentication failed"):
        super().__init__(
            message=message,
            status_code=status.HTTP_401_UNAUTHORIZED,
            error_code="AUTHENTICATION_ERROR",
        )


class AuthorizationException(InsightStreamException):
    """Exception raised for authorization errors."""

    def __init__(self, message: str = "Access denied"):
        super().__init__(
            message=message,
            status_code=status.HTTP_403_FORBIDDEN,
            error_code="AUTHORIZATION_ERROR",
        )


class ResourceNotFoundException(InsightStreamException):
    """Exception raised when a resource is not found."""

    def __init__(self, resource_type: str, resource_id: Any):
        message = f"{resource_type} with ID '{resource_id}' not found"
        super().__init__(
            message=message,
            status_code=status.HTTP_404_NOT_FOUND,
            details={"resource_type": resource_type, "resource_id": str(resource_id)},
            error_code="RESOURCE_NOT_FOUND",
        )


class ResourceConflictException(InsightStreamException):
    """Exception raised when a resource conflict occurs."""

    def __init__(self, message: str, resource_type: str | None = None):
        details = {}
        if resource_type:
            details["resource_type"] = resource_type

        super().__init__(
            message=message,
            status_code=status.HTTP_409_CONFLICT,
            details=details,
            error_code="RESOURCE_CONFLICT",
        )


class RateLimitException(InsightStreamException):
    """Exception raised when rate limit is exceeded."""

    def __init__(
        self, message: str = "Rate limit exceeded", retry_after: int | None = None
    ):
        details = {}
        if retry_after:
            details["retry_after"] = retry_after

        super().__init__(
            message=message,
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            details=details,
            error_code="RATE_LIMIT_EXCEEDED",
        )


class VideoProcessingException(InsightStreamException):
    """Exception raised during video processing."""

    def __init__(
        self, message: str, video_url: str | None = None, stage: str | None = None
    ):
        details = {}
        if video_url:
            details["video_url"] = video_url
        if stage:
            details["processing_stage"] = stage

        super().__init__(
            message=message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details=details,
            error_code="VIDEO_PROCESSING_ERROR",
        )


class AIServiceException(InsightStreamException):
    """Exception raised when AI service calls fail."""

    def __init__(
        self, message: str, service: str | None = None, model: str | None = None
    ):
        details = {}
        if service:
            details["service"] = service
        if model:
            details["model"] = model

        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details,
            error_code="AI_SERVICE_ERROR",
        )


class DatabaseException(InsightStreamException):
    """Exception raised for database-related errors."""

    def __init__(self, message: str, operation: str | None = None):
        details = {}
        if operation:
            details["operation"] = operation

        super().__init__(
            message=message,
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            details=details,
            error_code="DATABASE_ERROR",
        )


class ExternalServiceException(InsightStreamException):
    """Exception raised when external service calls fail."""

    def __init__(self, message: str, service: str, status_code: int | None = None):
        details = {"service": int(service)}
        if status_code:
            details["external_status_code"] = status_code

        super().__init__(
            message=message,
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            details=details,
            error_code="EXTERNAL_SERVICE_ERROR",
        )


async def insight_stream_exception_handler(
    request: Request, exc: InsightStreamException
) -> JSONResponse:
    """
    Handle InsightStream custom exceptions.

    Args:
        request: FastAPI request object
        exc: InsightStream exception instance

    Returns:
        JSONResponse: Formatted error response
    """
    # Log the exception
    logger.error(
        f"InsightStream exception: {exc.message}",
        extra={
            "error_code": exc.error_code,
            "status_code": exc.status_code,
            "details": exc.details,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # Log to Logfire
    logfire.error(
        "InsightStream exception occurred",
        error_code=exc.error_code,
        message=exc.message,
        status_code=exc.status_code,
        details=exc.details,
        path=str(request.url.path),
        method=request.method,
    )

    # Create error response
    error_response = {
        "error": {
            "code": exc.error_code,
            "message": exc.message,
            "details": exc.details,
        },
        "request_id": getattr(request.state, "request_id", None),
        "timestamp": logfire.current_time().isoformat(),
    }

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response,
    )


async def validation_exception_handler(
    request: Request, exc: ValidationError
) -> JSONResponse:
    """
    Handle Pydantic validation exceptions.

    Args:
        request: FastAPI request object
        exc: Pydantic validation exception

    Returns:
        JSONResponse: Formatted validation error response
    """
    # Extract validation errors
    errors = []
    for error in exc.errors():
        errors.append(
            {
                "field": ".".join(str(x) for x in error["loc"]),
                "message": error["msg"],
                "type": error["type"],
                "input": error.get("input"),
            }
        )

    # Log the validation error
    logger.warning(
        f"Validation error: {len(errors)} field(s) failed validation",
        extra={
            "errors": errors,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # Log to Logfire
    logfire.warning(
        "Validation error occurred",
        error_count=len(errors),
        errors=errors,
        path=str(request.url.path),
        method=request.method,
    )

    # Create error response
    error_response = {
        "error": {
            "code": "VALIDATION_ERROR",
            "message": "Request validation failed",
            "details": {
                "errors": errors,
                "error_count": len(errors),
            },
        },
        "request_id": getattr(request.state, "request_id", None),
        "timestamp": logfire.current_time().isoformat(),
    }

    return JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content=error_response,
    )


async def http_exception_handler(request: Request, exc: HTTPException) -> JSONResponse:
    """
    Handle FastAPI HTTP exceptions.

    Args:
        request: FastAPI request object
        exc: HTTP exception

    Returns:
        JSONResponse: Formatted HTTP error response
    """
    # Log the HTTP exception
    logger.warning(
        f"HTTP exception: {exc.status_code} - {exc.detail}",
        extra={
            "status_code": exc.status_code,
            "detail": exc.detail,
            "path": request.url.path,
            "method": request.method,
        },
    )

    # Create error response
    error_response = {
        "error": {
            "code": f"HTTP_{exc.status_code}",
            "message": exc.detail,
            "details": {},
        },
        "request_id": getattr(request.state, "request_id", None),
        "timestamp": logfire.current_time().isoformat(),
    }

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response,
    )
