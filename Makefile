# InsightStream - Development Makefile
# Unlock the Knowledge Within Video

.PHONY: help setup dev test lint clean build deploy

# Default target
help:
	@echo "InsightStream Development Commands"
	@echo "=================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  setup          - Initial project setup"
	@echo "  setup-backend  - Setup backend environment"
	@echo "  setup-frontend - Setup frontend environment"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev            - Start development environment"
	@echo "  dev-backend    - Start backend development server"
	@echo "  dev-frontend   - Start frontend development server"
	@echo "  dev-workers    - Start Celery workers"
	@echo ""
	@echo "Testing Commands:"
	@echo "  test           - Run all tests"
	@echo "  test-backend   - Run backend tests"
	@echo "  test-frontend  - Run frontend tests"
	@echo "  test-e2e       - Run end-to-end tests"
	@echo "  coverage       - Generate test coverage reports"
	@echo ""
	@echo "Code Quality Commands:"
	@echo "  lint           - Run linting and formatting"
	@echo "  lint-backend   - Lint backend code"
	@echo "  lint-frontend  - Lint frontend code"
	@echo "  format         - Format all code"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-init        - Initialize database"
	@echo "  db-migrate     - Run database migrations"
	@echo "  db-seed        - Seed database with test data"
	@echo "  db-reset       - Reset database"
	@echo "  db-connect     - Test Neon PostgreSQL connection"
	@echo ""
	@echo "Production Commands:"
	@echo "  build          - Build production artifacts"
	@echo "  deploy         - Deploy to production"
	@echo "  clean          - Clean build artifacts"

# Setup Commands
setup: setup-backend setup-frontend
	@echo "✅ InsightStream setup complete!"

setup-backend:
	@echo "🔧 Setting up backend environment..."
	cd backend && uv init --python 3.11
	@echo "📦 Installing production dependencies..."
	cd backend && uv add -r requirements.txt
	@echo "🛠️ Installing development dependencies..."
	cd backend && uv add --dev -r dev-requirements.txt
	@echo "✅ Backend setup complete!"

setup-frontend:
	@echo "🔧 Setting up frontend environment..."
	cd frontend && npm install
	@echo "🎨 Installing Tailwind CSS v4 (2025 latest)..."
	cd frontend && npm install tailwindcss @tailwindcss/vite
	@echo "🎤 Installing speech recognition packages..."
	cd frontend && npm install react-speech-recognition react-speakup
	@echo "🎨 Installing additional UI and utility packages..."
	cd frontend && npm install @headlessui/react @heroicons/react zustand @tanstack/react-query axios date-fns lodash framer-motion react-hot-toast
	@echo "✅ Frontend setup complete!"

# Development Commands
dev: dev-backend

dev-backend:
	@echo "🚀 Starting FastAPI backend development server..."
	cd backend && uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

dev-frontend:
	@echo "🚀 Starting React frontend development server..."
	cd frontend && npm run dev

dev-workers:
	@echo "🚀 Starting Celery workers..."
	cd backend && uv run celery -A app.core.celery worker --loglevel=info

dev-full:
	@echo "🚀 Starting full development environment..."
	@echo "Starting backend in background..."
	cd backend && uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000 &
	@echo "Starting frontend..."
	cd frontend && npm run dev

# Testing Commands
test: test-backend test-frontend
	@echo "✅ All tests completed!"

test-backend:
	@echo "🧪 Running backend tests..."
	cd backend && uv run pytest --cov=app --cov-report=term-missing

test-frontend:
	@echo "🧪 Running frontend tests..."
	cd frontend && npm test

test-e2e:
	@echo "🧪 Running end-to-end tests..."
	cd frontend && npm run e2e

coverage:
	@echo "📊 Generating coverage reports..."
	cd backend && uv run pytest --cov=app --cov-report=html --cov-report=xml
	cd frontend && npm run test:coverage
	@echo "📊 Coverage reports generated in backend/htmlcov/ and frontend/coverage/"

# Code Quality Commands
lint: lint-backend lint-frontend

lint-backend:
	@echo "🔍 Linting backend code..."
	cd backend && uv run ruff check .
	cd backend && uv run mypy app

lint-frontend:
	@echo "🔍 Linting frontend code..."
	cd frontend && npm run lint
	cd frontend && npm run type-check

format:
	@echo "🎨 Formatting code..."
	cd backend && uv run ruff format .
	cd backend && uv run ruff check --fix .
	cd frontend && npm run lint:fix
	@echo "✅ Code formatting complete!"

# Database Commands
db-init:
	@echo "🗄️ Initializing database..."
	cd backend && uv run python scripts/init_db.py

db-migrate:
	@echo "🗄️ Running database migrations..."
	cd backend && uv run alembic upgrade head

db-seed:
	@echo "🌱 Seeding database with test data..."
	cd backend && uv run python scripts/seed_data.py

db-reset:
	@echo "🗄️ Resetting database..."
	cd backend && uv run python scripts/init_db.py --reset
	$(MAKE) db-migrate
	$(MAKE) db-seed

# Database Commands (Neon PostgreSQL)
db-connect:
	@echo "🔗 Connecting to Neon PostgreSQL..."
	@echo "Make sure your DATABASE_URL is set in .env file"
	cd backend && uv run python -c "from app.core.database import engine; print('✅ Database connection successful!')"

# Production Commands
build: build-backend build-frontend
	@echo "✅ Production build complete!"

build-backend:
	@echo "🏗️ Building backend for production..."
	cd backend && docker build -t insightstream/api:latest .

build-frontend:
	@echo "🏗️ Building frontend for production..."
	cd frontend && npm run build
	cd frontend && docker build -t insightstream/frontend:latest .

deploy:
	@echo "🚀 Deploying to production..."
	kubectl apply -f k8s/
	kubectl rollout status deployment/insightstream-api -n insightstream
	kubectl rollout status deployment/insightstream-frontend -n insightstream
	@echo "✅ Deployment complete!"

clean:
	@echo "🧹 Cleaning build artifacts..."
	cd backend && rm -rf __pycache__ .pytest_cache .coverage htmlcov/
	cd frontend && rm -rf node_modules/.cache dist coverage/
	@echo "✅ Cleanup complete!"

# Utility Commands
logs:
	@echo "📋 Showing application logs..."
	docker-compose logs -f

logs-backend:
	@echo "📋 Showing backend logs..."
	docker-compose logs -f backend

logs-frontend:
	@echo "📋 Showing frontend logs..."
	docker-compose logs -f frontend

status:
	@echo "📊 Checking service status..."
	docker-compose ps

restart:
	@echo "🔄 Restarting services..."
	docker-compose restart

stop:
	@echo "⏹️ Stopping services..."
	docker-compose stop

# Development helpers
install-hooks:
	@echo "🪝 Installing git hooks..."
	cd backend && source venv/bin/activate && pre-commit install
	@echo "✅ Git hooks installed!"

update-deps:
	@echo "📦 Updating dependencies..."
	cd backend && source venv/bin/activate && pip-compile requirements.in
	cd backend && source venv/bin/activate && pip-compile requirements-dev.in
	cd frontend && npm update
	@echo "✅ Dependencies updated!"

security-check:
	@echo "🔒 Running security checks..."
	cd backend && source venv/bin/activate && safety check
	cd frontend && npm audit
	@echo "✅ Security check complete!"

# Quick development workflow
quick-start: setup db-connect
	@echo "🎉 InsightStream is ready for development!"
	@echo ""
	@echo "🚀 To start development:"
	@echo "  Backend: make dev-backend (FastAPI server)"
	@echo "  Frontend: make dev-frontend (React app)"
	@echo "  Full: make dev-full (both servers)"
	@echo ""
	@echo "🌐 Access URLs:"
	@echo "  Frontend: http://localhost:3000"
	@echo "  Backend API: http://localhost:8000"
	@echo "  API Documentation: http://localhost:8000/docs"
	@echo ""
	@echo "📝 Next steps:"
	@echo "  1. Edit your .env file with your Neon PostgreSQL URL and API keys"
	@echo "  2. Run 'make db-migrate' to set up database schema"
	@echo "  3. Run 'make db-seed' to add test data"
	@echo "  4. Start building amazing features!"

# Development workflow with testing
dev-workflow: format lint test
	@echo "✅ Development workflow complete - ready to commit!"
