"""
FastAPI Main Application

This module contains the main FastAPI application instance with all middleware,
routers, and configuration for the InsightStream backend.
"""

from contextlib import asynccontextmanager
from typing import AsyncGenerator

import logfire
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from rich.console import Console
from rich.logging import <PERSON>Handler

from app.core.config import get_settings
from app.core.database import create_db_and_tables, get_engine
from app.core.exceptions import (
    InsightStreamException,
    insight_stream_exception_handler,
    validation_exception_handler,
)
from app.core.logging import setup_logging

# Initialize Rich console for beautiful terminal output
console = Console()

# Get application settings
settings = get_settings()


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """
    Application lifespan manager.

    Handles startup and shutdown events for the FastAPI application.
    """
    # Startup
    console.print("🚀 [bold green]Starting InsightStream Backend...[/bold green]")

    # Setup logging
    setup_logging()

    # Initialize Logfire
    if settings.LOGFIRE_TOKEN:
        logfire.configure(
            token=settings.LOGFIRE_TOKEN,
            project_name="insightstream",
            environment=settings.ENVIRONMENT,
        )
        logfire.info("Logfire initialized successfully")

    # Initialize database
    try:
        await create_db_and_tables()
        console.print("✅ [bold green]Database initialized successfully[/bold green]")
        logfire.info("Database initialized successfully")
    except Exception as e:
        console.print(f"❌ [bold red]Database initialization failed: {e}[/bold red]")
        logfire.error("Database initialization failed", error=str(e))
        raise

    console.print("🎯 [bold blue]InsightStream Backend is ready![/bold blue]")

    yield

    # Shutdown
    console.print(
        "🛑 [bold yellow]Shutting down InsightStream Backend...[/bold yellow]"
    )

    # Close database connections
    engine = get_engine()
    await engine.dispose()

    console.print("👋 [bold green]InsightStream Backend shutdown complete[/bold green]")


# Create FastAPI application instance
app = FastAPI(
    title="InsightStream API",
    description="AI-powered universal video interaction platform",
    version="0.1.0",
    docs_url="/docs" if settings.ENVIRONMENT == "development" else None,
    redoc_url="/redoc" if settings.ENVIRONMENT == "development" else None,
    openapi_url="/openapi.json" if settings.ENVIRONMENT != "production" else None,
    lifespan=lifespan,
)

# Add middleware
if settings.ENVIRONMENT == "production":
    app.add_middleware(
        TrustedHostMiddleware,
        allowed_hosts=settings.ALLOWED_HOSTS,
    )

app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Add exception handlers
app.add_exception_handler(InsightStreamException, insight_stream_exception_handler)
app.add_exception_handler(ValueError, validation_exception_handler)


@app.middleware("http")
async def log_requests(request: Request, call_next) -> Response:
    """Log all HTTP requests with timing information."""
    import time

    start_time = time.perf_counter()

    # Log request
    logfire.info(
        "HTTP request started",
        method=request.method,
        url=str(request.url),
        client_ip=request.client.host if request.client else None,
    )

    # Process request
    response = await call_next(request)

    # Calculate processing time
    process_time = time.perf_counter() - start_time

    # Log response
    logfire.info(
        "HTTP request completed",
        method=request.method,
        url=str(request.url),
        status_code=response.status_code,
        process_time=process_time,
    )

    # Add timing header
    response.headers["X-Process-Time"] = str(process_time)

    return response


# Health check endpoints
@app.get("/health", tags=["Health"])
async def health_check() -> dict[str, str]:
    """Health check endpoint for load balancers and monitoring."""
    return {"status": "healthy", "service": "insightstream-api"}


@app.get("/ready", tags=["Health"])
async def readiness_check() -> dict[str, str]:
    """Readiness check endpoint to verify all dependencies are available."""
    try:
        # Check database connection
        engine = get_engine()
        async with engine.begin() as conn:
            await conn.execute("SELECT 1")

        return {"status": "ready", "service": "insightstream-api"}
    except Exception as e:
        logfire.error("Readiness check failed", error=str(e))
        return JSONResponse(
            status_code=503, content={"status": "not ready", "error": str(e)}
        )


@app.get("/", tags=["Root"])
async def root() -> dict[str, str]:
    """Root endpoint with API information."""
    return {
        "message": "Welcome to InsightStream API",
        "version": "0.1.0",
        "docs": "/docs" if settings.ENVIRONMENT == "development" else "disabled",
        "health": "/health",
        "ready": "/ready",
    }


# Import and include API routers
# Note: These will be created in subsequent tasks
# from app.api.v1.auth import router as auth_router
# from app.api.v1.videos import router as videos_router
# from app.api.v1.ai import router as ai_router
# from app.api.v1.notes import router as notes_router
# from app.api.v1.sessions import router as sessions_router
# from app.api.v1.voice import router as voice_router
# from app.api.v1.analytics import router as analytics_router
# from app.api.v1.websocket import router as websocket_router

# app.include_router(auth_router, prefix="/api/v1/auth", tags=["Authentication"])
# app.include_router(videos_router, prefix="/api/v1/videos", tags=["Videos"])
# app.include_router(ai_router, prefix="/api/v1/ai", tags=["AI"])
# app.include_router(notes_router, prefix="/api/v1/notes", tags=["Notes"])
# app.include_router(sessions_router, prefix="/api/v1/sessions", tags=["Sessions"])
# app.include_router(voice_router, prefix="/api/v1/voice", tags=["Voice"])
# app.include_router(analytics_router, prefix="/api/v1/analytics", tags=["Analytics"])
# app.include_router(websocket_router, prefix="/ws", tags=["WebSocket"])


if __name__ == "__main__":
    import uvicorn

    console.print("🔧 [bold cyan]Running in development mode[/bold cyan]")
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info",
    )
