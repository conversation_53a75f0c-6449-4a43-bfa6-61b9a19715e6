"""
Database Configuration and Connection Management

This module handles database connections, session management, and table creation
using SQLModel with asyncpg for PostgreSQL.
"""

from typing import Any, AsyncGenerator

from sqlalchemy.ext.asyncio import AsyncEngine
from sqlalchemy.orm import sessionmaker
from sqlmodel import SQLModel, create_engine, text
from sqlmodel.ext.asyncio import AsyncSession

from app.core.config import get_settings
from app.core.exceptions import DatabaseException
from app.core.logging import get_logger

# Get application settings
settings = get_settings()

# Get logger instance
logger = get_logger(__name__)

# Global engine instance
_engine: AsyncEngine | None = None


def get_engine() -> AsyncEngine:
    """
    Get or create the database engine.

    Returns:
        AsyncEngine: SQLAlchemy async engine instance

    Raises:
        DatabaseException: If DATABASE_URL is not configured or engine creation fails
    """
    global _engine

    if _engine is None:
        if not settings.DATABASE_URL:
            raise DatabaseException(
                "DATABASE_URL is not configured", operation="engine_creation"
            )

        try:
            # Create async engine with asyncpg
            _engine = AsyncEngine(
                create_engine(
                    str(settings.DATABASE_URL),
                    echo=settings.DATABASE_ECHO,
                    pool_size=settings.DATABASE_POOL_SIZE,
                    max_overflow=settings.DATABASE_MAX_OVERFLOW,
                    pool_timeout=settings.DATABASE_POOL_TIMEOUT,
                    pool_pre_ping=True,  # Verify connections before use
                    pool_recycle=settings.DATABASE_POOL_RECYCLE,
                )
            )

            logger.info(
                "Database engine created successfully",
                extra={
                    "pool_size": settings.DATABASE_POOL_SIZE,
                    "max_overflow": settings.DATABASE_MAX_OVERFLOW,
                    "pool_timeout": settings.DATABASE_POOL_TIMEOUT,
                    "pool_recycle": settings.DATABASE_POOL_RECYCLE,
                },
            )

        except Exception as e:
            raise DatabaseException(
                f"Failed to create database engine: {e}", operation="engine_creation"
            ) from e

    return _engine


# Create async session factory
async_session_factory = sessionmaker(
    class_=AsyncSession,
    bind=get_engine(),
    expire_on_commit=False,
)


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """
    Dependency to get database session.

    Yields:
        AsyncSession: Database session for dependency injection

    Raises:
        DatabaseException: If session creation or management fails
    """
    try:
        async with async_session_factory() as session:
            try:
                yield session
            except Exception as e:
                await session.rollback()
                logger.error(
                    "Database session error occurred",
                    extra={"error": str(e), "error_type": type(e).__name__},
                )
                raise DatabaseException(
                    f"Database session error: {e}", operation="session_management"
                ) from e
            finally:
                await session.close()
    except Exception as e:
        if not isinstance(e, DatabaseException):
            logger.error(
                "Failed to create database session",
                extra={"error": str(e), "error_type": type(e).__name__},
            )
            raise DatabaseException(
                f"Failed to create database session: {e}", operation="session_creation"
            ) from e
        raise


async def create_db_and_tables() -> None:
    """
    Create database tables if they don't exist.

    This function should be called during application startup.

    Raises:
        DatabaseException: If table creation fails
    """
    try:
        engine = get_engine()

        # Import all models to ensure they are registered with SQLModel
        # Note: These imports will be added as models are created
        # from app.models.user import User, UserSession, UserNote
        # from app.models.video import Video, Transcript, TranscriptSegment
        # from app.models.ai_interaction import AIInteraction, AIInteractionFollowUp
        # from app.models.voice_interaction import VoiceInteraction
        # from app.models.analytics import UserAnalytics

        async with engine.begin() as conn:
            # Create all tables
            await conn.run_sync(SQLModel.metadata.create_all)

        logger.info("Database tables created successfully")

    except Exception as e:
        logger.error(
            "Failed to create database tables",
            extra={"error": str(e), "error_type": type(e).__name__},
        )
        raise DatabaseException(
            f"Failed to create database tables: {e}", operation="table_creation"
        ) from e


async def drop_db_and_tables() -> None:
    """
    Drop all database tables.

    WARNING: This will delete all data! Use only for testing or development.

    Raises:
        DatabaseException: If running in production or table dropping fails
    """
    if settings.is_production:
        raise DatabaseException(
            "Cannot drop tables in production environment", operation="table_drop"
        )

    try:
        engine = get_engine()

        async with engine.begin() as conn:
            # Drop all tables
            await conn.run_sync(SQLModel.metadata.drop_all)

        logger.warning("All database tables dropped")

    except Exception as e:
        logger.error(
            "Failed to drop database tables",
            extra={"error": str(e), "error_type": type(e).__name__},
        )
        raise DatabaseException(
            f"Failed to drop database tables: {e}", operation="table_drop"
        ) from e


async def check_db_connection() -> bool:
    """
    Check if database connection is working.

    Returns:
        bool: True if connection is successful, False otherwise
    """
    try:
        engine = get_engine()

        async with engine.begin() as conn:
            # Simple query to test connection
            result = await conn.execute(text("SELECT 1"))
            await result.fetchone()

        logger.info("Database connection check successful")
        return True

    except Exception as e:
        logger.error(
            "Database connection check failed",
            extra={"error": str(e), "error_type": type(e).__name__},
        )
        return False


class DatabaseManager:
    """
    Database manager for handling connections and transactions.
    """

    def __init__(self) -> None:
        self.engine = get_engine()

    async def execute_query(
        self, query: str, params: dict[str, Any] | None = None
    ) -> Any:
        """
        Execute a raw SQL query.

        Args:
            query: SQL query string
            params: Query parameters

        Returns:
            Query result

        Raises:
            DatabaseException: If query execution fails
        """
        try:
            async with async_session_factory() as session:
                try:
                    result = await session.execute(text(query), params or {})
                    await session.commit()
                    return result
                except Exception as e:
                    await session.rollback()
                    logger.error(
                        "Query execution failed",
                        extra={
                            "query": query,
                            "params": params,
                            "error": str(e),
                            "error_type": type(e).__name__,
                        },
                    )
                    raise DatabaseException(
                        f"Query execution failed: {e}", operation="query_execution"
                    ) from e
        except Exception as e:
            if not isinstance(e, DatabaseException):
                raise DatabaseException(
                    f"Failed to execute query: {e}", operation="query_execution"
                ) from e
            raise

    async def get_table_info(self, table_name: str) -> dict[str, Any]:
        """
        Get information about a database table.

        Args:
            table_name: Name of the table

        Returns:
            dict: Table information

        Raises:
            DatabaseException: If table info retrieval fails
        """
        query = """
        SELECT
            column_name,
            data_type,
            is_nullable,
            column_default
        FROM information_schema.columns
        WHERE table_name = :table_name
        ORDER BY ordinal_position;
        """

        try:
            result = await self.execute_query(query, {"table_name": table_name})
            columns = []
            for row in result:
                columns.append(
                    {
                        "name": row[0],
                        "type": row[1],
                        "nullable": row[2] == "YES",
                        "default": row[3],
                    }
                )

            return {
                "table_name": table_name,
                "columns": columns,
            }

        except Exception as e:
            logger.error(
                "Failed to get table info",
                extra={
                    "table_name": table_name,
                    "error": str(e),
                    "error_type": type(e).__name__,
                },
            )
            raise DatabaseException(
                f"Failed to get table info for {table_name}: {e}",
                operation="table_info_retrieval",
            ) from e

    async def get_database_stats(self) -> dict[str, Any]:
        """
        Get database statistics.

        Returns:
            dict: Database statistics
        """
        queries = {
            "total_tables": "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public'",
            "database_size": "SELECT pg_size_pretty(pg_database_size(current_database()))",
            "active_connections": "SELECT COUNT(*) FROM pg_stat_activity WHERE state = 'active'",
        }

        stats: dict[str, Any] = {}

        for stat_name, query in queries.items():
            try:
                result = await self.execute_query(query)
                row = await result.fetchone()
                stats[stat_name] = row[0] if row else None
            except Exception as e:
                logger.warning(
                    f"Failed to get database statistic: {stat_name}",
                    extra={
                        "stat_name": stat_name,
                        "query": query,
                        "error": str(e),
                        "error_type": type(e).__name__,
                    },
                )
                stats[stat_name] = None

        return stats


# Global database manager instance
db_manager = DatabaseManager()
