# InsightStream Backend Makefile
# Provides convenient commands for development, testing, and deployment

.PHONY: help install install-dev test test-setup lint format clean dev build docker-build docker-run

# Default target
help:
	@echo "InsightStream Backend Development Commands"
	@echo "=========================================="
	@echo ""
	@echo "Setup Commands:"
	@echo "  install      Install production dependencies"
	@echo "  install-dev  Install development dependencies"
	@echo "  setup        Initial project setup"
	@echo ""
	@echo "Development Commands:"
	@echo "  dev          Start development server"
	@echo "  test-setup   Test basic application setup"
	@echo "  test         Run all tests"
	@echo "  lint         Run code linting"
	@echo "  format       Format code"
	@echo ""
	@echo "Database Commands:"
	@echo "  db-init      Initialize database"
	@echo "  db-migrate   Run database migrations"
	@echo "  db-upgrade   Upgrade database to latest migration"
	@echo "  db-reset     Reset database (WARNING: deletes all data)"
	@echo ""
	@echo "Utility Commands:"
	@echo "  clean        Clean temporary files"
	@echo "  build        Build production package"
	@echo "  docker-build Build Docker image"
	@echo "  docker-run   Run Docker container"

# Installation commands
install:
	@echo "📦 Installing production dependencies..."
	uv sync --no-dev

install-dev:
	@echo "📦 Installing development dependencies..."
	uv sync

setup: install-dev
	@echo "🔧 Setting up InsightStream backend..."
	@if [ ! -f .env ]; then \
		echo "📋 Creating .env file from template..."; \
		cp .env.example .env; \
		echo "⚠️  Please edit .env file with your configuration"; \
	fi
	@echo "✅ Setup complete!"

# Development commands
dev:
	@echo "🚀 Starting development server..."
	uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

test-setup:
	@echo "🧪 Testing basic application setup..."
	uv run python test_setup.py

test:
	@echo "🧪 Running tests..."
	uv run pytest tests/ -v --cov=app --cov-report=html --cov-report=term

test-unit:
	@echo "🧪 Running unit tests..."
	uv run pytest tests/unit/ -v

test-integration:
	@echo "🧪 Running integration tests..."
	uv run pytest tests/integration/ -v

test-e2e:
	@echo "🧪 Running end-to-end tests..."
	uv run pytest tests/e2e/ -v

# Code quality commands
lint:
	@echo "🔍 Running code linting..."
	uv run ruff check .
	uv run mypy app

lint-fix:
	@echo "🔧 Fixing linting issues..."
	uv run ruff check --fix .

format:
	@echo "🎨 Formatting code..."
	uv run ruff format .

format-check:
	@echo "🎨 Checking code formatting..."
	uv run ruff format --check .

# Database commands
db-init:
	@echo "🗄️ Initializing database..."
	uv run python -c "import asyncio; from app.core.database import create_db_and_tables; asyncio.run(create_db_and_tables())"

db-migrate:
	@echo "🗄️ Creating database migration..."
	uv run alembic revision --autogenerate -m "$(MSG)"

db-upgrade:
	@echo "🗄️ Upgrading database..."
	uv run alembic upgrade head

db-downgrade:
	@echo "🗄️ Downgrading database..."
	uv run alembic downgrade -1

db-reset:
	@echo "⚠️  Resetting database (this will delete all data)..."
	@read -p "Are you sure? [y/N] " -n 1 -r; \
	if [[ $$REPLY =~ ^[Yy]$$ ]]; then \
		echo ""; \
		uv run python -c "import asyncio; from app.core.database import drop_db_and_tables, create_db_and_tables; asyncio.run(drop_db_and_tables()); asyncio.run(create_db_and_tables())"; \
	else \
		echo ""; \
		echo "Database reset cancelled."; \
	fi

# Utility commands
clean:
	@echo "🧹 Cleaning temporary files..."
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
	find . -type d -name "*.egg-info" -exec rm -rf {} +
	find . -type f -name ".coverage" -delete
	rm -rf htmlcov/
	rm -rf .pytest_cache/
	rm -rf .mypy_cache/
	rm -rf .ruff_cache/
	rm -rf dist/
	rm -rf build/

build:
	@echo "📦 Building production package..."
	uv build

# Docker commands
docker-build:
	@echo "🐳 Building Docker image..."
	docker build -t insightstream/backend:latest .

docker-run:
	@echo "🐳 Running Docker container..."
	docker run -p 8000:8000 --env-file .env insightstream/backend:latest

docker-dev:
	@echo "🐳 Running Docker container in development mode..."
	docker run -p 8000:8000 -v $(PWD):/app --env-file .env insightstream/backend:latest

# Security commands
security-check:
	@echo "🔒 Running security checks..."
	uv run bandit -r app/
	uv run safety check

# Performance commands
profile:
	@echo "📊 Running performance profiling..."
	uv run python -m cProfile -o profile.stats -m uvicorn app.main:app --host 0.0.0.0 --port 8000

# Documentation commands
docs:
	@echo "📚 Generating documentation..."
	uv run mkdocs build

docs-serve:
	@echo "📚 Serving documentation..."
	uv run mkdocs serve

# Dependency management
deps-update:
	@echo "📦 Updating dependencies..."
	uv lock --upgrade

deps-check:
	@echo "📦 Checking for outdated dependencies..."
	uv tree

# Environment commands
env-check:
	@echo "🔍 Checking environment configuration..."
	uv run python -c "from app.core.config import get_settings; settings = get_settings(); print(f'Environment: {settings.ENVIRONMENT}'); print(f'Debug: {settings.DEBUG}'); print(f'Database URL: {settings.DATABASE_URL}')"

# Celery commands
celery-worker:
	@echo "👷 Starting Celery worker..."
	uv run celery -A app.core.celery worker --loglevel=info

celery-beat:
	@echo "⏰ Starting Celery beat scheduler..."
	uv run celery -A app.core.celery beat --loglevel=info

celery-flower:
	@echo "🌸 Starting Celery Flower monitoring..."
	uv run celery -A app.core.celery flower

# All-in-one commands
dev-full: install-dev db-upgrade
	@echo "🚀 Starting full development environment..."
	@echo "Starting Celery worker in background..."
	uv run celery -A app.core.celery worker --loglevel=info --detach
	@echo "Starting development server..."
	uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

check-all: lint format-check test security-check
	@echo "✅ All checks completed!"
