# Universal Video Interaction Platform - System Diagrams

## 1. System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[React + TypeScript UI]
        VP[Video Player Component]
        TC[Transcript Component]
        NC[Notes Component]
        AC[AI Chat Component]
        VC[Voice Component]
    end

    subgraph "API Gateway"
        AG[FastAPI Gateway]
        WS[WebSocket Server]
        RT[Rate Limiter]
    end

    subgraph "Multi-Agent AI System"
        VA[Video Analysis Agent]
        QA[Q&A Agent]
        NA[Note Assistant Agent]
        TA[Transcript Agent]
        SP[Speech Processor Agent]
        CD[Content Discovery Agent]
    end

    subgraph "Core Services"
        VS[Video Service]
        TS[Transcript Service]
        NS[Notes Service]
        AS[Analytics Service]
        SS[Search Service]
    end

    subgraph "Data Layer"
        PG[(PostgreSQL)]
        RD[(Redis Cache)]
        QD[(Qdrant Vector DB)]
        S3[(S3 Storage)]
    end

    subgraph "External Services"
        YT[YouTube API]
        TT[TikTok API]
        LI[LinkedIn API]
        FB[Facebook API]
        WH[Whisper API]
        EL[ElevenLabs TTS]
    end

    subgraph "Monitoring & Logging"
        LF[Logfire]
        PR[Prometheus]
        GR[Grafana]
        SE[Sentry]
    end

    UI --> AG
    VP --> WS
    TC --> AG
    NC --> AG
    AC --> AG
    VC --> AG

    AG --> RT
    RT --> VS
    RT --> TS
    RT --> NS
    RT --> AS
    RT --> SS

    VS --> VA
    TS --> TA
    NS --> NA
    AS --> QA
    SS --> CD

    VA --> PG
    QA --> QD
    NA --> PG
    TA --> PG
    SP --> S3
    CD --> QD

    VS --> YT
    VS --> TT
    VS --> LI
    VS --> FB
    TS --> WH
    SP --> EL

    AG --> LF
    VS --> PR
    UI --> SE
```

## 2. Multi-Agent AI Architecture

```mermaid
graph LR
    subgraph "User Interaction Layer"
        UI[User Interface]
        VC[Voice Commands]
        TC[Text Chat]
        NC[Note Creation]
    end

    subgraph "Agent Orchestrator"
        AO[Agent Orchestrator]
        CM[Context Manager]
        RM[Response Merger]
    end

    subgraph "Specialized AI Agents"
        subgraph "Video Analysis Agent"
            VA1[Content Analyzer]
            VA2[Topic Extractor]
            VA3[Summary Generator]
        end

        subgraph "Q&A Agent"
            QA1[Query Processor]
            QA2[Context Retriever]
            QA3[Answer Generator]
        end

        subgraph "Note Assistant Agent"
            NA1[Note Analyzer]
            NA2[Content Enhancer]
            NA3[Structure Optimizer]
        end

        subgraph "Transcript Agent"
            TA1[Text Processor]
            TA2[Timing Aligner]
            TA3[Quality Enhancer]
        end

        subgraph "Speech Processor Agent"
            SP1[STT Processor]
            SP2[TTS Generator]
            SP3[Voice Command Parser]
        end

        subgraph "Content Discovery Agent"
            CD1[Similarity Analyzer]
            CD2[Connection Finder]
            CD3[Recommendation Engine]
        end
    end

    subgraph "Knowledge Base"
        VDB[(Vector Database)]
        TDB[(Transcript Database)]
        NDB[(Notes Database)]
        UDB[(User Context)]
    end

    UI --> AO
    VC --> AO
    TC --> AO
    NC --> AO

    AO --> CM
    CM --> VA1
    CM --> QA1
    CM --> NA1
    CM --> TA1
    CM --> SP1
    CM --> CD1

    VA1 --> VA2 --> VA3
    QA1 --> QA2 --> QA3
    NA1 --> NA2 --> NA3
    TA1 --> TA2 --> TA3
    SP1 --> SP2 --> SP3
    CD1 --> CD2 --> CD3

    VA3 --> RM
    QA3 --> RM
    NA3 --> RM
    TA3 --> RM
    SP3 --> RM
    CD3 --> RM

    QA2 --> VDB
    CD1 --> VDB
    TA1 --> TDB
    NA1 --> NDB
    CM --> UDB

    RM --> UI
```

## 3. Database Entity Relationship Diagram

```mermaid
erDiagram
    USERS {
        int id PK
        string email UK
        string username UK
        string hashed_password
        string full_name
        boolean is_active
        boolean is_verified
        json preferences
        datetime created_at
        datetime updated_at
    }

    VIDEOS {
        int id PK
        enum platform
        string external_id UK
        string url
        string title
        text description
        float duration
        string thumbnail_url
        string channel_name
        string channel_id
        int view_count
        int like_count
        datetime upload_date
        string language
        json tags
        json metadata
        string processing_status
        text ai_summary
        json key_topics
        datetime created_at
        datetime updated_at
    }

    TRANSCRIPTS {
        int id PK
        int video_id FK
        string language
        string source
        float confidence_score
        int word_count
        float processing_time
        boolean enhanced_by_ai
        datetime created_at
    }

    TRANSCRIPT_SEGMENTS {
        int id PK
        int transcript_id FK
        float start_time
        float end_time
        text text
        float confidence
        string speaker
        json word_timestamps
        boolean is_highlighted
        string highlight_color
    }

    USER_SESSIONS {
        int id PK
        int user_id FK
        int video_id FK
        string session_name
        float last_position
        float total_watch_time
        int interaction_count
        int notes_count
        int ai_queries_count
        json session_data
        boolean is_active
        datetime started_at
        datetime last_accessed
    }

    USER_NOTES {
        int id PK
        int user_id FK
        int video_id FK
        int session_id FK
        string title
        text content
        string content_type
        float timestamp
        float duration
        json tags
        boolean is_ai_generated
        float ai_confidence
        boolean is_favorite
        boolean is_shared
        string color_theme
        json position_data
        datetime created_at
        datetime updated_at
    }

    NOTE_MEDIA_ATTACHMENTS {
        int id PK
        int note_id FK
        enum media_type
        string file_path
        string file_name
        int file_size
        string mime_type
        float duration
        text transcript
        json metadata
        datetime created_at
    }

    AI_INTERACTIONS {
        int id PK
        int user_id FK
        int video_id FK
        int session_id FK
        enum agent_type
        text query
        text response
        float context_start
        float context_end
        json context_segments
        float confidence_score
        float processing_time
        int tokens_used
        float cost
        int feedback_rating
        text feedback_comment
        boolean is_bookmarked
        json metadata
        datetime created_at
    }

    AI_INTERACTION_FOLLOW_UPS {
        int id PK
        int parent_interaction_id FK
        text suggested_question
        string question_type
        float relevance_score
        boolean is_used
        datetime created_at
    }

    VOICE_INTERACTIONS {
        int id PK
        int user_id FK
        int video_id FK
        int session_id FK
        string interaction_type
        string audio_file_path
        text transcribed_text
        string synthesized_audio_path
        string language
        float confidence_score
        float processing_time
        float timestamp
        json metadata
        datetime created_at
    }

    VIDEO_CONNECTIONS {
        int id PK
        int source_video_id FK
        int target_video_id FK
        string connection_type
        float similarity_score
        json shared_topics
        boolean ai_generated
        boolean user_confirmed
        datetime created_at
    }

    USER_ANALYTICS {
        int id PK
        int user_id FK
        datetime date
        int videos_watched
        float total_watch_time
        int notes_created
        int ai_interactions
        int voice_interactions
        float average_session_duration
        json most_used_features
        json learning_progress
        datetime created_at
    }

    USERS ||--o{ USER_SESSIONS : "has"
    USERS ||--o{ USER_NOTES : "creates"
    USERS ||--o{ AI_INTERACTIONS : "initiates"
    USERS ||--o{ VOICE_INTERACTIONS : "performs"
    USERS ||--o{ USER_ANALYTICS : "generates"

    VIDEOS ||--o| TRANSCRIPTS : "has"
    VIDEOS ||--o{ USER_SESSIONS : "viewed_in"
    VIDEOS ||--o{ USER_NOTES : "annotated_with"
    VIDEOS ||--o{ AI_INTERACTIONS : "discussed_in"
    VIDEOS ||--o{ VOICE_INTERACTIONS : "spoken_about"
    VIDEOS ||--o{ VIDEO_CONNECTIONS : "connected_to"

    TRANSCRIPTS ||--o{ TRANSCRIPT_SEGMENTS : "contains"

    USER_SESSIONS ||--o{ USER_NOTES : "contains"
    USER_SESSIONS ||--o{ AI_INTERACTIONS : "includes"
    USER_SESSIONS ||--o{ VOICE_INTERACTIONS : "records"

    USER_NOTES ||--o{ NOTE_MEDIA_ATTACHMENTS : "has"

    AI_INTERACTIONS ||--o{ AI_INTERACTION_FOLLOW_UPS : "suggests"
```

## 4. Video Processing Workflow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant API as FastAPI
    participant VS as Video Service
    participant TS as Transcript Service
    participant VA as Video Analysis Agent
    participant TA as Transcript Agent
    participant DB as Database
    participant EXT as External APIs
    participant LF as Logfire

    U->>UI: Submit video URL
    UI->>API: POST /videos/analyze
    API->>LF: Log request start

    API->>VS: Extract video metadata
    VS->>EXT: Call yt-dlp/platform API
    EXT-->>VS: Return video info
    VS->>DB: Store video metadata
    VS-->>API: Video created

    API->>TS: Extract transcript (async)
    TS->>EXT: Call transcript API/Whisper
    EXT-->>TS: Return transcript data
    TS->>TA: Enhance transcript
    TA->>TA: Process timing & quality
    TA->>DB: Store transcript segments

    API->>VA: Analyze content (async)
    VA->>VA: Generate summary & topics
    VA->>DB: Store AI analysis

    TS-->>API: Transcript ready
    VA-->>API: Analysis complete
    API->>LF: Log processing complete
    API-->>UI: Processing status update
    UI-->>U: Show video ready
```

## 5. AI Q&A Interaction Flow

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant API as FastAPI
    participant QA as Q&A Agent
    participant CM as Context Manager
    participant VDB as Vector DB
    participant DB as Database
    participant LF as Logfire

    U->>UI: Ask question about video
    UI->>API: POST /ai/ask
    API->>LF: Log AI request

    API->>CM: Prepare context
    CM->>DB: Get transcript segments
    CM->>DB: Get user notes
    CM->>VDB: Search similar content
    CM-->>API: Context prepared

    API->>QA: Process question with context
    QA->>QA: Analyze query intent
    QA->>QA: Generate response
    QA->>QA: Extract source timestamps
    QA->>QA: Suggest follow-up questions
    QA-->>API: Structured response

    API->>DB: Store interaction
    API->>LF: Log response generated
    API-->>UI: Return AI response
    UI-->>U: Display answer with sources

    Note over U,UI: User can click timestamps to jump to video position
    Note over QA,VDB: RAG-based contextual understanding
```

## 6. Note Creation with AI Assistance

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant VC as Voice Component
    participant API as FastAPI
    participant NA as Note Assistant Agent
    participant SP as Speech Processor Agent
    participant DB as Database
    participant S3 as File Storage
    participant LF as Logfire

    alt Voice Note Creation
        U->>VC: Start voice recording
        VC->>VC: Record audio
        U->>VC: Stop recording
        VC->>API: POST /notes/voice-create
        API->>SP: Process audio
        SP->>SP: Convert speech to text
        SP->>S3: Store audio file
        SP-->>API: Transcribed text
    else Text Note Creation
        U->>UI: Type note content
        UI->>API: POST /notes/create
    end

    API->>LF: Log note creation start
    API->>NA: Enhance note content
    NA->>NA: Analyze content
    NA->>NA: Add structure & formatting
    NA->>NA: Suggest tags & categories
    NA->>NA: Link to video timestamps
    NA-->>API: Enhanced note

    API->>DB: Store note with metadata
    API->>LF: Log note created
    API-->>UI: Return created note
    UI-->>U: Display enhanced note

    Note over NA: AI suggests improvements, tags, and connections
    Note over SP: Supports multiple languages and accents
```

## 7. User Session Management Flow

```mermaid
stateDiagram-v2
    [*] --> SessionStart

    SessionStart --> VideoLoading : User submits video URL
    VideoLoading --> VideoReady : Processing complete
    VideoLoading --> ProcessingError : Processing fails

    VideoReady --> Watching : User starts playback
    VideoReady --> Browsing : User explores interface

    Watching --> Paused : User pauses video
    Watching --> NoteTaking : User creates note
    Watching --> AIChat : User asks question
    Watching --> VoiceInteraction : User speaks command

    Paused --> Watching : User resumes
    Paused --> NoteTaking : User creates note
    Paused --> AIChat : User asks question

    NoteTaking --> Watching : Note saved
    NoteTaking --> AIChat : AI assists with note

    AIChat --> Watching : Question answered
    AIChat --> NoteTaking : AI suggests note

    VoiceInteraction --> Watching : Command processed
    VoiceInteraction --> NoteTaking : Voice note created
    VoiceInteraction --> AIChat : Voice question asked

    Browsing --> Watching : User starts video
    Browsing --> HistoryView : User views history

    HistoryView --> SessionRestore : User selects session
    SessionRestore --> Watching : Session restored

    Watching --> SessionEnd : User leaves
    NoteTaking --> SessionEnd : User leaves
    AIChat --> SessionEnd : User leaves
    Paused --> SessionEnd : User leaves
    ProcessingError --> SessionEnd : User leaves

    SessionEnd --> [*]

    note right of SessionStart
        Session data includes:
        - Video position
        - Notes created
        - AI interactions
        - Voice recordings
        - User preferences
    end note

    note right of SessionRestore
        Complete context restoration:
        - Last video position
        - All notes and highlights
        - AI conversation history
        - Voice interaction history
    end note
```

## 8. Multi-Platform Video Processing Architecture

```mermaid
graph TB
    subgraph "Video Input Sources"
        YT[YouTube URLs]
        TT[TikTok URLs]
        LI[LinkedIn URLs]
        FB[Facebook URLs]
        IG[Instagram URLs]
        CU[Custom Upload]
    end

    subgraph "Platform Adapters"
        YTA[YouTube Adapter]
        TTA[TikTok Adapter]
        LIA[LinkedIn Adapter]
        FBA[Facebook Adapter]
        IGA[Instagram Adapter]
        CUA[Custom Adapter]
    end

    subgraph "Processing Pipeline"
        VE[Video Extractor]
        MD[Metadata Parser]
        TE[Transcript Extractor]
        QC[Quality Checker]
        EN[Content Enhancer]
    end

    subgraph "AI Processing"
        VA[Video Analysis Agent]
        TA[Transcript Agent]
        CD[Content Discovery Agent]
    end

    subgraph "Storage Layer"
        VS[Video Storage]
        TS[Transcript Storage]
        MS[Metadata Storage]
        AS[Analytics Storage]
    end

    subgraph "External Services"
        YTAPI[YouTube API]
        TTAPI[TikTok API]
        LIAPI[LinkedIn API]
        FBAPI[Facebook API]
        IGAPI[Instagram API]
        WHAPI[Whisper API]
        ELAPI[ElevenLabs API]
    end

    YT --> YTA
    TT --> TTA
    LI --> LIA
    FB --> FBA
    IG --> IGA
    CU --> CUA

    YTA --> VE
    TTA --> VE
    LIA --> VE
    FBA --> VE
    IGA --> VE
    CUA --> VE

    VE --> MD
    MD --> TE
    TE --> QC
    QC --> EN

    EN --> VA
    EN --> TA
    EN --> CD

    VA --> VS
    TA --> TS
    CD --> MS
    VA --> AS

    YTA --> YTAPI
    TTA --> TTAPI
    LIA --> LIAPI
    FBA --> FBAPI
    IGA --> IGAPI
    TE --> WHAPI
    EN --> ELAPI

    classDef platform fill:#e1f5fe
    classDef adapter fill:#f3e5f5
    classDef processing fill:#e8f5e8
    classDef ai fill:#fff3e0
    classDef storage fill:#fce4ec
    classDef external fill:#f1f8e9

    class YT,TT,LI,FB,IG,CU platform
    class YTA,TTA,LIA,FBA,IGA,CUA adapter
    class VE,MD,TE,QC,EN processing
    class VA,TA,CD ai
    class VS,TS,MS,AS storage
    class YTAPI,TTAPI,LIAPI,FBAPI,IGAPI,WHAPI,ELAPI external
```

## 9. Real-time Synchronization Architecture

```mermaid
sequenceDiagram
    participant VP as Video Player
    participant WS as WebSocket Server
    participant TS as Transcript Service
    participant UI as UI Components
    participant DB as Database

    VP->>WS: Connect to video session
    WS->>DB: Get transcript segments
    DB-->>WS: Return segments
    WS-->>VP: Send initial transcript

    loop Video Playback
        VP->>WS: Send current time update
        WS->>TS: Find active segment
        TS-->>WS: Return active segment
        WS->>UI: Broadcast active segment
        UI->>UI: Highlight current text
        UI->>UI: Auto-scroll transcript
    end

    alt User clicks transcript
        UI->>WS: Seek to timestamp
        WS->>VP: Send seek command
        VP->>VP: Jump to position
        VP->>WS: Confirm position
        WS->>UI: Update UI state
    end

    alt User creates note
        UI->>WS: Create note at timestamp
        WS->>DB: Store note
        DB-->>WS: Confirm storage
        WS->>UI: Broadcast note created
        UI->>UI: Show note indicator
    end

    Note over VP,UI: Real-time synchronization ensures<br/>seamless user experience
```

## 10. Deployment Architecture

```mermaid
graph TB
    subgraph "Load Balancer"
        LB[Nginx Load Balancer]
        SSL[SSL Termination]
    end

    subgraph "Frontend Tier"
        FE1[React App Instance 1]
        FE2[React App Instance 2]
        FE3[React App Instance 3]
        CDN[CloudFlare CDN]
    end

    subgraph "API Tier"
        API1[FastAPI Instance 1]
        API2[FastAPI Instance 2]
        API3[FastAPI Instance 3]
        WS1[WebSocket Server 1]
        WS2[WebSocket Server 2]
    end

    subgraph "AI Processing Tier"
        AG1[Agno Agent Pool 1]
        AG2[Agno Agent Pool 2]
        AG3[Agno Agent Pool 3]
        GPU[GPU Processing Nodes]
    end

    subgraph "Background Processing"
        CW1[Celery Worker 1]
        CW2[Celery Worker 2]
        CW3[Celery Worker 3]
        RQ[Redis Queue]
    end

    subgraph "Data Tier"
        PG1[(PostgreSQL Primary)]
        PG2[(PostgreSQL Replica)]
        RD1[(Redis Cluster 1)]
        RD2[(Redis Cluster 2)]
        QD1[(Qdrant Vector DB)]
        S3[(S3 Storage)]
    end

    subgraph "Monitoring"
        LF[Logfire]
        PR[Prometheus]
        GR[Grafana]
        AL[Alertmanager]
    end

    Internet --> LB
    LB --> SSL
    SSL --> CDN
    CDN --> FE1
    CDN --> FE2
    CDN --> FE3

    FE1 --> API1
    FE2 --> API2
    FE3 --> API3

    API1 --> WS1
    API2 --> WS2

    API1 --> AG1
    API2 --> AG2
    API3 --> AG3

    AG1 --> GPU
    AG2 --> GPU
    AG3 --> GPU

    API1 --> RQ
    API2 --> RQ
    API3 --> RQ

    RQ --> CW1
    RQ --> CW2
    RQ --> CW3

    API1 --> PG1
    API2 --> PG1
    API3 --> PG1
    PG1 --> PG2

    API1 --> RD1
    API2 --> RD2
    API3 --> RD1

    AG1 --> QD1
    AG2 --> QD1
    AG3 --> QD1

    CW1 --> S3
    CW2 --> S3
    CW3 --> S3

    API1 --> LF
    API2 --> LF
    API3 --> LF

    LF --> PR
    PR --> GR
    PR --> AL


```
