# InsightStream Backend

> **FastAPI backend with multi-agent AI system for universal video interaction**

This is the backend service for InsightStream, built with FastAPI and featuring a sophisticated multi-agent AI system powered by Agno.com and Groq API.

## 🚧 Current Implementation Status

✅ **Task 1.1.1.1 COMPLETED**: Initialize FastAPI project with SQLModel and asyncpg
- FastAPI application with async support
- SQLModel integration for type-safe database operations
- asyncpg for high-performance PostgreSQL connections
- Comprehensive configuration management
- Structured logging with Logfire and Rich
- Exception handling and error responses
- Development environment setup with Makefile

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+**
- **uv** - Modern Python package manager ([install guide](https://docs.astral.sh/uv/))
- **Neon PostgreSQL** database
- **Redis** for caching and task queue

### Setup

```bash
# Install dependencies with current versions
uv add -r requirements.txt

# Install development dependencies
uv add --dev -r dev-requirements.txt

# Copy environment file
cp ../.env.example .env

# Configure your environment variables
# Edit .env with your database URL and API keys

# Run database migrations
uv run alembic upgrade head

# Start the development server
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

### Access Points

- **API Server**: <http://localhost:8000>
- **API Documentation**: <http://localhost:8000/docs>
- **ReDoc Documentation**: <http://localhost:8000/redoc>

## 🏗️ Architecture

### Core Components

```text
backend/
├── app/
│   ├── main.py              # FastAPI application entry point
│   ├── core/                # Core configuration and utilities
│   │   ├── config.py        # Environment configuration
│   │   ├── database.py      # Database connection and session
│   │   ├── security.py      # Authentication and authorization
│   │   └── logging.py       # Logfire integration
│   ├── models/              # SQLModel database models
│   │   ├── user.py          # User model
│   │   ├── video.py         # Video model
│   │   ├── note.py          # Note model
│   │   └── session.py       # Session model
│   ├── api/                 # API route handlers
│   │   ├── v1/              # API version 1
│   │   │   ├── auth.py      # Authentication endpoints
│   │   │   ├── videos.py    # Video management endpoints
│   │   │   ├── ai.py        # AI interaction endpoints
│   │   │   └── notes.py     # Note management endpoints
│   ├── services/            # Business logic services
│   │   ├── ai/              # AI service layer
│   │   │   ├── agents/      # Multi-agent system
│   │   │   ├── groq.py      # Groq API integration
│   │   │   ├── agno.py      # Agno.com integration
│   │   │   └── embeddings.py # Sentence Transformers
│   │   ├── video/           # Video processing services
│   │   │   ├── downloader.py # yt-dlp video downloading
│   │   │   ├── transcript.py # YouTube transcript API
│   │   │   └── processor.py  # Video analysis and processing
│   │   ├── transcript.py    # Transcript processing
│   │   └── knowledge.py     # Knowledge graph service
│   ├── workers/             # Background task workers
│   │   ├── celery_app.py    # Celery configuration
│   │   ├── video_tasks.py   # Video processing tasks
│   │   └── ai_tasks.py      # AI processing tasks
│   └── utils/               # Utility functions
│       ├── video_utils.py   # Video processing utilities
│       ├── text_utils.py    # Text processing utilities
│       └── cache.py         # Redis caching utilities
├── tests/                   # Test suite
│   ├── unit/                # Unit tests
│   ├── integration/         # Integration tests
│   └── e2e/                 # End-to-end tests
├── alembic/                 # Database migrations
├── pyproject.toml           # Project configuration
└── README.md               # This file
```

## 🤖 AI System

### Multi-Agent Architecture

InsightStream uses a sophisticated multi-agent AI system:

#### Core Agents

1. **Video Analysis Agent** - Analyzes video content and metadata
2. **Q&A Agent** - Handles contextual questions about videos
3. **Note Assistant Agent** - Enhances and organizes user notes
4. **Transcript Agent** - Processes and analyzes transcripts
5. **Speech Processor Agent** - Handles voice interactions
6. **Content Discovery Agent** - Finds related content and connections

#### AI Stack

- **Agno.com**: Multi-agent orchestration and coordination
- **Groq API**: Fast, free LLM inference (Llama 3 8B)
- **Pydantic-AI**: Structured AI interactions and data validation
- **Sentence Transformers**: Free embeddings (all-MiniLM-L6-v2)
- **Qdrant**: Vector database for RAG capabilities

### Configuration

```bash
# AI Services
AGNO_API_KEY=your-agno-api-key
GROQ_API_KEY=your-groq-api-key  # Free tier available

# LLM Configuration
PRIMARY_LLM_PROVIDER=groq
GROQ_MODEL=llama3-8b-8192
FALLBACK_LLM_PROVIDER=agno

# Embeddings (Free)
EMBEDDING_MODEL=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_PROVIDER=huggingface
EMBEDDING_DIMENSION=384
```

## 🎥 Video Processing

### Modern Video Downloading with yt-dlp

InsightStream uses **yt-dlp** (modern replacement for youtube-dl) for robust video downloading:

#### Supported Platforms

- **YouTube**: Full support including live streams, playlists, channels
- **TikTok**: Video and metadata extraction
- **LinkedIn**: Professional video content
- **Facebook**: Public video content
- **Instagram**: Reels and video posts
- **Twitter/X**: Video tweets and threads
- **Vimeo**: Professional video platform
- **Twitch**: Live streams and VODs
- **And 1000+ more sites**: Complete list at [yt-dlp supported sites](https://github.com/yt-dlp/yt-dlp/blob/master/supportedsites.md)

#### Configuration (yt-dlp)

```bash
# yt-dlp Configuration
YTDLP_FORMAT=best[height<=720]        # Quality preference
YTDLP_WRITESUBTITLES=true            # Download subtitles
YTDLP_WRITEAUTOMATICSUB=true         # Download auto-generated subs
YTDLP_SUBTITLESLANGS=en,en-US        # Subtitle languages
YTDLP_IGNOREERRORS=true              # Continue on errors
```

### YouTube Transcript API

For YouTube videos, we use **youtube-transcript-api** for reliable transcript extraction:

#### Features

- **Multiple Languages**: Support for all YouTube transcript languages
- **Auto-generated Transcripts**: Fallback to auto-generated when manual unavailable
- **Timestamp Preservation**: Accurate timing information
- **Error Handling**: Graceful fallback when transcripts unavailable

#### Configuration (youtube-transcript-api)

```bash
# YouTube Transcript API
YOUTUBE_TRANSCRIPT_LANGUAGES=["en", "en-US", "en-GB"]
YOUTUBE_TRANSCRIPT_PRESERVE_FORMATTING=false
YOUTUBE_TRANSCRIPT_PROXIES=null
```

### Video Processing Pipeline

1. **URL Analysis**: Detect platform and extract metadata
2. **Download**: Use yt-dlp for video and subtitle download
3. **Transcript Extraction**:
   - YouTube: Use youtube-transcript-api first
   - Other platforms: Extract from downloaded subtitles
   - Fallback: Use Whisper for audio transcription
4. **AI Analysis**: Process with multi-agent AI system
5. **Knowledge Extraction**: Build knowledge graph from content

## 📊 Database Schema

### Core Models

- **User**: User accounts and authentication
- **Video**: Video metadata and processing status
- **Note**: User notes with AI enhancements
- **Session**: User interaction sessions
- **Transcript**: Video transcripts and analysis
- **Knowledge**: Knowledge graph entities and relationships

### Database Setup

```bash
# Create migration
uv run alembic revision --autogenerate -m "Description"

# Apply migrations
uv run alembic upgrade head

# Rollback migration
uv run alembic downgrade -1
```

## 🔧 Development

### Environment Variables

```bash
# Database
DATABASE_URL=postgresql+asyncpg://user:pass@host:port/dbname

# AI Services
AGNO_API_KEY=your-agno-api-key
GROQ_API_KEY=your-groq-api-key

# Redis
REDIS_URL=redis://localhost:6379/0

# Monitoring
LOGFIRE_TOKEN=your-logfire-token

# Security
SECRET_KEY=your-secret-key
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
```

### Development Commands

```bash
# Development server with hot reload
uv run uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# Run tests
uv run pytest                              # All tests
uv run pytest --cov=app                   # With coverage
uv run pytest tests/unit/                 # Unit tests only
uv run pytest tests/integration/          # Integration tests only

# Code quality
uv run ruff format .                       # Format code
uv run ruff check .                        # Lint code
uv run ruff check --fix .                  # Auto-fix issues
uv run mypy app                           # Type checking

# Database operations
uv run alembic upgrade head               # Apply migrations
uv run alembic revision --autogenerate   # Create migration

# Background workers
uv run celery -A app.workers.celery_app worker --loglevel=info
```

## 🧪 Testing

### Test Structure

```bash
tests/
├── unit/                    # Unit tests for individual components
│   ├── test_models.py       # Database model tests
│   ├── test_services.py     # Service layer tests
│   └── test_utils.py        # Utility function tests
├── integration/             # Integration tests
│   ├── test_api.py          # API endpoint tests
│   ├── test_ai.py           # AI service integration tests
│   └── test_database.py     # Database integration tests
└── e2e/                     # End-to-end tests
    ├── test_video_flow.py   # Complete video processing flow
    └── test_ai_flow.py      # AI interaction flow
```

### Running Tests

```bash
# All tests with coverage
uv run pytest --cov=app --cov-report=html

# Specific test categories
uv run pytest tests/unit/                 # Fast unit tests
uv run pytest tests/integration/          # Integration tests
uv run pytest tests/e2e/                  # End-to-end tests

# Test specific functionality
uv run pytest tests/unit/test_ai.py       # AI-related tests
uv run pytest tests/integration/test_api.py # API tests
```

## 🚀 Deployment

### Production Setup

```bash
# Build production image
docker build -t insightstream/api:latest .

# Run with docker-compose
docker-compose up -d

# Or deploy to cloud platform
# See ../docs/PRODUCTION_DEPLOYMENT_GUIDE.md for details
```

### Environment Configuration

```bash
# Production environment variables
DEBUG=false
ENVIRONMENT=production
DATABASE_URL=postgresql+asyncpg://prod_user:prod_pass@prod_host:5432/insightstream
REDIS_URL=redis://prod_redis:6379/0
```

## 📈 Monitoring

### Logfire Integration

The backend includes comprehensive monitoring with Logfire:

```python
import logfire

# Automatic FastAPI instrumentation
logfire.configure()
logfire.instrument_fastapi(app)

# Custom logging
logfire.info("Processing video", video_id=video_id)
logfire.error("AI processing failed", error=str(e))
```

### Health Checks

- **Health Endpoint**: `/health` - Basic health check
- **Ready Endpoint**: `/ready` - Database and Redis connectivity
- **Metrics Endpoint**: `/metrics` - Prometheus metrics

## 🔒 Security

### Authentication

- **JWT Tokens**: Secure user authentication
- **Password Hashing**: bcrypt for password security
- **Rate Limiting**: API rate limiting for abuse prevention

### Data Protection

- **Input Validation**: Pydantic models for all inputs
- **SQL Injection Prevention**: SQLModel ORM protection
- **CORS Configuration**: Proper cross-origin resource sharing

## 📚 API Documentation

### Interactive Documentation

- **Swagger UI**: <http://localhost:8000/docs>
- **ReDoc**: <http://localhost:8000/redoc>

### Key Endpoints

- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/videos/` - Upload and process videos
- `POST /api/v1/ai/chat` - AI chat interactions
- `GET /api/v1/notes/` - Retrieve user notes
- `POST /api/v1/ai/enhance-note` - AI note enhancement

## 🤝 Contributing

1. **Setup Development Environment**

   ```bash
   # Install current versions of all dependencies
   uv add -r requirements.txt
   uv add --dev -r dev-requirements.txt

   cp ../.env.example .env
   # Configure .env file
   ```

2. **Run Tests Before Committing**

   ```bash
   uv run pytest
   uv run ruff check .
   uv run mypy app
   ```

3. **Follow Code Standards**
   - Use type hints for all functions
   - Write tests for new features
   - Update documentation for API changes
   - Follow FastAPI best practices

## 📞 Support

For backend-specific issues:

- **Setup Problems**: Check environment variables and database connection
- **AI Integration**: Verify Groq and Agno API keys
- **Performance**: Monitor Logfire dashboard for bottlenecks
- **Database**: Check Alembic migrations and connection

---

**InsightStream Backend** - *Powering AI-driven video interaction* 🚀
