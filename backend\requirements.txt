# Core FastAPI and Web Framework
fastapi
uvicorn[standard]
python-multipart
python-jose[cryptography]
passlib[bcrypt]
python-dotenv

# Database and ORM
sqlmodel
alembic
asyncpg
psycopg2-binary

# Background Tasks and Caching
celery
redis
kombu

# AI and Machine Learning
agno
pydantic-ai
groq
openai
sentence-transformers
transformers
torch
numpy
scikit-learn

# Video Processing and Transcription
yt-dlp
youtube-transcript-api
whisper

# Vector Database and Search
qdrant-client
chromadb

# Text Processing and NLP
beautifulsoup4
lxml
spacy
nltk

# HTTP Client and Utilities
httpx
aiofiles
requests

# Data Validation and Serialization
pydantic
pydantic-settings

# Monitoring and Logging
logfire
rich
structlog

# Image Processing
pillow

# Optional: Premium TTS (if needed)
elevenlabs

# Optional: Advanced Graph Database
neo4j

# Optional: Additional ML Libraries
langchain
langchain-community
langchain-openai
langchain-groq

# Optional: Advanced Analytics
pandas
matplotlib
seaborn
plotly

# Optional: Document Processing
pypdf2
python-docx
openpyxl

# Optional: Advanced Video Processing
opencv-python
moviepy

# Optional: Audio Processing
librosa
soundfile
pydub

# Optional: Web Scraping
selenium
playwright

# Optional: API Rate Limiting
slowapi

# Optional: Advanced Caching
diskcache
cachetools

# Optional: Configuration Management
dynaconf

# Optional: Task Scheduling
apscheduler

# Optional: Metrics and Monitoring
prometheus-client
statsd

# Optional: Security
cryptography
pyotp
qrcode

# Optional: Email
fastapi-mail
jinja2

# Optional: File Storage
boto3
minio

# Optional: Advanced Validation
email-validator
phonenumbers

# Optional: Internationalization
babel

# Optional: Advanced Logging
python-json-logger

# Optional: Performance Monitoring
py-spy
memory-profiler

# Optional: Testing Utilities (for runtime testing)
factory-boy
faker
