# Production Deployment & Operations Guide

## Production Architecture Overview

### Infrastructure Requirements

- **Compute**: Kubernetes cluster with auto-scaling (minimum 3 nodes)
- **Database**: PostgreSQL 15+ with read replicas and automated backups
- **Cache**: Redis cluster with high availability
- **Storage**: S3-compatible object storage with CDN
- **Monitoring**: Comprehensive observability stack
- **Security**: WAF, DDoS protection, SSL/TLS termination

### Deployment Strategy

- **Blue-Green Deployment**: Zero-downtime deployments
- **Canary Releases**: Gradual rollout of new features
- **Feature Flags**: Runtime feature toggling
- **Database Migrations**: Automated, reversible schema changes
- **Health Checks**: Comprehensive application health monitoring

## Kubernetes Deployment Configuration

### 1. Namespace and Resource Quotas

```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: video-interaction
  labels:
    name: video-interaction
    environment: production

---
apiVersion: v1
kind: ResourceQuota
metadata:
  name: video-interaction-quota
  namespace: video-interaction
spec:
  hard:
    requests.cpu: "20"
    requests.memory: 40Gi
    limits.cpu: "40"
    limits.memory: 80Gi
    persistentvolumeclaims: "10"
    services: "20"
    secrets: "20"
    configmaps: "20"
```

### 2. ConfigMap and Secrets

```yaml
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: video-interaction-config
  namespace: video-interaction
data:
  ENVIRONMENT: "production"
  API_V1_PREFIX: "/api/v1"
  CORS_ORIGINS: '["https://app.videointeraction.com"]'
  REDIS_URL: "redis://redis-cluster:6379/0"
  CELERY_BROKER_URL: "redis://redis-cluster:6379/1"
  LOGFIRE_ENVIRONMENT: "production"
  PROMETHEUS_ENABLED: "true"

---
apiVersion: v1
kind: Secret
metadata:
  name: video-interaction-secrets
  namespace: video-interaction
type: Opaque
data:
  DATABASE_URL: <base64-encoded-database-url>
  SECRET_KEY: <base64-encoded-secret-key>
  AGNO_API_KEY: <base64-encoded-agno-key>
  OPENAI_API_KEY: <base64-encoded-openai-key>
  ELEVENLABS_API_KEY: <base64-encoded-elevenlabs-key>
  AWS_ACCESS_KEY_ID: <base64-encoded-aws-key>
  AWS_SECRET_ACCESS_KEY: <base64-encoded-aws-secret>
  LOGFIRE_TOKEN: <base64-encoded-logfire-token>
  SENTRY_DSN: <base64-encoded-sentry-dsn>
```

### 3. Backend API Deployment

```yaml
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-interaction-api
  namespace: video-interaction
  labels:
    app: video-interaction-api
    version: v1
spec:
  replicas: 3
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  selector:
    matchLabels:
      app: video-interaction-api
  template:
    metadata:
      labels:
        app: video-interaction-api
        version: v1
    spec:
      containers:
      - name: api
        image: videointeraction/api:latest
        ports:
        - containerPort: 8000
          name: http
        - containerPort: 8001
          name: metrics
        env:
        - name: DATABASE_URL
          valueFrom:
            secretKeyRef:
              name: video-interaction-secrets
              key: DATABASE_URL
        - name: SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: video-interaction-secrets
              key: SECRET_KEY
        envFrom:
        - configMapRef:
            name: video-interaction-config
        - secretRef:
            name: video-interaction-secrets
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8000
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /ready
            port: 8000
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: video-interaction-uploads

---
apiVersion: v1
kind: Service
metadata:
  name: video-interaction-api-service
  namespace: video-interaction
  labels:
    app: video-interaction-api
spec:
  selector:
    app: video-interaction-api
  ports:
  - name: http
    port: 80
    targetPort: 8000
  - name: metrics
    port: 8001
    targetPort: 8001
  type: ClusterIP

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: video-interaction-uploads
  namespace: video-interaction
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 100Gi
  storageClassName: fast-ssd
```

### 4. Celery Worker Deployment

```yaml
# k8s/celery-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-interaction-celery
  namespace: video-interaction
  labels:
    app: video-interaction-celery
spec:
  replicas: 5
  selector:
    matchLabels:
      app: video-interaction-celery
  template:
    metadata:
      labels:
        app: video-interaction-celery
    spec:
      containers:
      - name: celery-worker
        image: videointeraction/api:latest
        command: ["celery", "-A", "app.core.celery", "worker", "--loglevel=info", "--concurrency=4"]
        envFrom:
        - configMapRef:
            name: video-interaction-config
        - secretRef:
            name: video-interaction-secrets
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: uploads
          mountPath: /app/uploads
      volumes:
      - name: uploads
        persistentVolumeClaim:
          claimName: video-interaction-uploads

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-interaction-celery-beat
  namespace: video-interaction
spec:
  replicas: 1
  selector:
    matchLabels:
      app: video-interaction-celery-beat
  template:
    metadata:
      labels:
        app: video-interaction-celery-beat
    spec:
      containers:
      - name: celery-beat
        image: videointeraction/api:latest
        command: ["celery", "-A", "app.core.celery", "beat", "--loglevel=info"]
        envFrom:
        - configMapRef:
            name: video-interaction-config
        - secretRef:
            name: video-interaction-secrets
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "200m"
```

### 5. Frontend Deployment

```yaml
# k8s/frontend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: video-interaction-frontend
  namespace: video-interaction
  labels:
    app: video-interaction-frontend
spec:
  replicas: 3
  selector:
    matchLabels:
      app: video-interaction-frontend
  template:
    metadata:
      labels:
        app: video-interaction-frontend
    spec:
      containers:
      - name: frontend
        image: videointeraction/frontend:latest
        ports:
        - containerPort: 80
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /
            port: 80
          initialDelaySeconds: 5
          periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: video-interaction-frontend-service
  namespace: video-interaction
spec:
  selector:
    app: video-interaction-frontend
  ports:
  - port: 80
    targetPort: 80
  type: ClusterIP
```

### 6. Ingress Configuration

```yaml
# k8s/ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: video-interaction-ingress
  namespace: video-interaction
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
spec:
  tls:
  - hosts:
    - app.videointeraction.com
    - api.videointeraction.com
    secretName: video-interaction-tls
  rules:
  - host: app.videointeraction.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: video-interaction-frontend-service
            port:
              number: 80
  - host: api.videointeraction.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: video-interaction-api-service
            port:
              number: 80
```

## Database Configuration

### 1. PostgreSQL High Availability Setup

```yaml
# k8s/postgresql.yaml
apiVersion: postgresql.cnpg.io/v1
kind: Cluster
metadata:
  name: postgres-cluster
  namespace: video-interaction
spec:
  instances: 3
  
  postgresql:
    parameters:
      max_connections: "200"
      shared_buffers: "256MB"
      effective_cache_size: "1GB"
      maintenance_work_mem: "64MB"
      checkpoint_completion_target: "0.9"
      wal_buffers: "16MB"
      default_statistics_target: "100"
      random_page_cost: "1.1"
      effective_io_concurrency: "200"
      work_mem: "4MB"
      min_wal_size: "1GB"
      max_wal_size: "4GB"
      
  bootstrap:
    initdb:
      database: video_interaction
      owner: app_user
      secret:
        name: postgres-credentials
        
  storage:
    size: 500Gi
    storageClass: fast-ssd
    
  monitoring:
    enabled: true
    
  backup:
    retentionPolicy: "30d"
    barmanObjectStore:
      destinationPath: "s3://video-interaction-backups/postgres"
      s3Credentials:
        accessKeyId:
          name: backup-credentials
          key: ACCESS_KEY_ID
        secretAccessKey:
          name: backup-credentials
          key: SECRET_ACCESS_KEY
      wal:
        retention: "7d"
      data:
        retention: "30d"
```

### 2. Redis Cluster Configuration

```yaml
# k8s/redis.yaml
apiVersion: redis.redis.opstreelabs.in/v1beta1
kind: RedisCluster
metadata:
  name: redis-cluster
  namespace: video-interaction
spec:
  clusterSize: 6
  clusterVersion: v7
  persistenceEnabled: true
  redisExporter:
    enabled: true
    image: quay.io/opstree/redis-exporter:1.0
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 50Gi
        storageClassName: fast-ssd
  resources:
    requests:
      memory: "1Gi"
      cpu: "500m"
    limits:
      memory: "2Gi"
      cpu: "1000m"
```

## Monitoring and Observability

### 1. Prometheus Configuration

```yaml
# k8s/prometheus.yaml
apiVersion: monitoring.coreos.com/v1
kind: Prometheus
metadata:
  name: video-interaction-prometheus
  namespace: video-interaction
spec:
  serviceAccountName: prometheus
  serviceMonitorSelector:
    matchLabels:
      app: video-interaction
  ruleSelector:
    matchLabels:
      app: video-interaction
  resources:
    requests:
      memory: 2Gi
      cpu: 1000m
    limits:
      memory: 4Gi
      cpu: 2000m
  retention: 30d
  storage:
    volumeClaimTemplate:
      spec:
        accessModes: ["ReadWriteOnce"]
        resources:
          requests:
            storage: 100Gi
        storageClassName: fast-ssd

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: video-interaction-api-monitor
  namespace: video-interaction
  labels:
    app: video-interaction
spec:
  selector:
    matchLabels:
      app: video-interaction-api
  endpoints:
  - port: metrics
    interval: 30s
    path: /metrics
```

### 2. Grafana Dashboard Configuration

```yaml
# k8s/grafana.yaml
apiVersion: integreatly.org/v1alpha1
kind: Grafana
metadata:
  name: video-interaction-grafana
  namespace: video-interaction
spec:
  config:
    auth:
      disable_login_form: false
    security:
      admin_user: admin
      admin_password: <secure-password>
  dashboards:
    - name: video-interaction-dashboard
      json: |
        {
          "dashboard": {
            "title": "Video Interaction Platform",
            "panels": [
              {
                "title": "API Response Time",
                "type": "graph",
                "targets": [
                  {
                    "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
                    "legendFormat": "95th percentile"
                  }
                ]
              },
              {
                "title": "Active Users",
                "type": "stat",
                "targets": [
                  {
                    "expr": "sum(rate(user_sessions_total[5m]))",
                    "legendFormat": "Active Sessions"
                  }
                ]
              }
            ]
          }
        }
```

## CI/CD Pipeline

### 1. GitHub Actions Workflow

```yaml
# .github/workflows/production-deploy.yml
name: Production Deployment

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

env:
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'
    
    - name: Install backend dependencies
      run: |
        cd backend
        pip install -r requirements.txt
        pip install -r requirements-dev.txt
    
    - name: Run backend tests
      run: |
        cd backend
        pytest --cov=app --cov-report=xml --cov-report=html
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
    
    - name: Install frontend dependencies
      run: |
        cd frontend
        npm ci
    
    - name: Run frontend tests
      run: |
        cd frontend
        npm run test:coverage
    
    - name: Run E2E tests
      run: |
        cd frontend
        npm run e2e:ci

  build-and-push:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: ./backend
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/api:latest,${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/api:${{ github.sha }}
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: ./frontend
        push: true
        tags: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:latest,${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}/frontend:${{ github.sha }}

  deploy:
    needs: build-and-push
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Configure kubectl
      uses: azure/k8s-set-context@v3
      with:
        method: kubeconfig
        kubeconfig: ${{ secrets.KUBE_CONFIG }}
    
    - name: Deploy to Kubernetes
      run: |
        kubectl apply -f k8s/
        kubectl rollout status deployment/video-interaction-api -n video-interaction
        kubectl rollout status deployment/video-interaction-frontend -n video-interaction
    
    - name: Run smoke tests
      run: |
        kubectl run smoke-test --image=curlimages/curl --rm -i --restart=Never -- \
          curl -f http://video-interaction-api-service/health
```

This comprehensive production deployment guide ensures a robust, scalable, and maintainable deployment of the universal video interaction platform with enterprise-grade reliability and monitoring.
