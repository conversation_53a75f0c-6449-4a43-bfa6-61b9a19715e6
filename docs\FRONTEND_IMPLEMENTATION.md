# Frontend Implementation Guide

## React + TypeScript Architecture

### Core Component Structure

```typescript
// src/types/video.ts
export interface Video {
  id: number;
  platform: 'youtube' | 'tiktok' | 'linkedin' | 'facebook';
  external_id: string;
  title: string;
  description?: string;
  duration: number;
  thumbnail_url?: string;
  channel_name?: string;
  created_at: string;
}

export interface TranscriptSegment {
  id: number;
  start_time: number;
  end_time: number;
  text: string;
  confidence?: number;
  isHighlighted?: boolean;
  isActive?: boolean;
}

export interface AIResponse {
  answer: string;
  confidence: number;
  source_timestamps: number[];
  related_topics: string[];
  follow_up_questions: string[];
}

export interface UserNote {
  id: number;
  video_id: number;
  timestamp: number;
  content: string;
  created_at: string;
}
```

### Custom Video Player Component

```typescript
// src/components/VideoPlayer/VideoPlayer.tsx
import React, { useRef, useEffect, useState, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX, Maximize, Settings } from 'lucide-react';
import { useVideoStore } from '../../stores/videoStore';
import { TranscriptSegment } from '../../types/video';

interface VideoPlayerProps {
  videoUrl: string;
  transcript: TranscriptSegment[];
  onTimeUpdate?: (currentTime: number) => void;
  onSegmentClick?: (segment: TranscriptSegment) => void;
}

export const VideoPlayer: React.FC<VideoPlayerProps> = ({
  videoUrl,
  transcript,
  onTimeUpdate,
  onSegmentClick
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [isMuted, setIsMuted] = useState(false);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);

  const { activeSegment, setActiveSegment } = useVideoStore();

  // Handle time updates and sync with transcript
  const handleTimeUpdate = useCallback(() => {
    if (!videoRef.current) return;
    
    const time = videoRef.current.currentTime;
    setCurrentTime(time);
    onTimeUpdate?.(time);

    // Find active transcript segment
    const activeSegment = transcript.find(
      segment => time >= segment.start_time && time <= segment.end_time
    );
    
    if (activeSegment) {
      setActiveSegment(activeSegment);
    }
  }, [transcript, onTimeUpdate, setActiveSegment]);

  // Seek to specific time (called from transcript clicks)
  const seekTo = useCallback((time: number) => {
    if (videoRef.current) {
      videoRef.current.currentTime = time;
      setCurrentTime(time);
    }
  }, []);

  // Play/pause toggle
  const togglePlayPause = useCallback(() => {
    if (!videoRef.current) return;
    
    if (isPlaying) {
      videoRef.current.pause();
    } else {
      videoRef.current.play();
    }
    setIsPlaying(!isPlaying);
  }, [isPlaying]);

  // Volume control
  const handleVolumeChange = useCallback((newVolume: number) => {
    if (!videoRef.current) return;
    
    videoRef.current.volume = newVolume;
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  }, []);

  // Mute toggle
  const toggleMute = useCallback(() => {
    if (!videoRef.current) return;
    
    if (isMuted) {
      videoRef.current.volume = volume;
      setIsMuted(false);
    } else {
      videoRef.current.volume = 0;
      setIsMuted(true);
    }
  }, [isMuted, volume]);

  // Playback speed control
  const changePlaybackRate = useCallback((rate: number) => {
    if (!videoRef.current) return;
    
    videoRef.current.playbackRate = rate;
    setPlaybackRate(rate);
  }, []);

  // Fullscreen toggle
  const toggleFullscreen = useCallback(() => {
    if (!document.fullscreenElement) {
      videoRef.current?.requestFullscreen();
      setIsFullscreen(true);
    } else {
      document.exitFullscreen();
      setIsFullscreen(false);
    }
  }, []);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (e.target !== document.body) return;
      
      switch (e.code) {
        case 'Space':
          e.preventDefault();
          togglePlayPause();
          break;
        case 'ArrowLeft':
          e.preventDefault();
          seekTo(Math.max(0, currentTime - 10));
          break;
        case 'ArrowRight':
          e.preventDefault();
          seekTo(Math.min(duration, currentTime + 10));
          break;
        case 'KeyM':
          e.preventDefault();
          toggleMute();
          break;
        case 'KeyF':
          e.preventDefault();
          toggleFullscreen();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [togglePlayPause, currentTime, duration, seekTo, toggleMute, toggleFullscreen]);

  // Format time display
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className="relative bg-black rounded-lg overflow-hidden shadow-2xl">
      {/* Video Element */}
      <video
        ref={videoRef}
        src={videoUrl}
        className="w-full h-auto"
        onTimeUpdate={handleTimeUpdate}
        onLoadedMetadata={() => setDuration(videoRef.current?.duration || 0)}
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        onClick={togglePlayPause}
      />

      {/* Custom Controls Overlay */}
      <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4">
        {/* Progress Bar */}
        <div className="mb-4">
          <div className="relative h-2 bg-white/20 rounded-full cursor-pointer"
               onClick={(e) => {
                 const rect = e.currentTarget.getBoundingClientRect();
                 const percent = (e.clientX - rect.left) / rect.width;
                 seekTo(percent * duration);
               }}>
            <div 
              className="absolute top-0 left-0 h-full bg-blue-500 rounded-full transition-all"
              style={{ width: `${(currentTime / duration) * 100}%` }}
            />
            {/* Transcript segment markers */}
            {transcript.map((segment) => (
              <div
                key={segment.id}
                className="absolute top-0 h-full w-1 bg-yellow-400/60 cursor-pointer hover:bg-yellow-400"
                style={{ left: `${(segment.start_time / duration) * 100}%` }}
                onClick={(e) => {
                  e.stopPropagation();
                  seekTo(segment.start_time);
                  onSegmentClick?.(segment);
                }}
                title={segment.text.substring(0, 100) + '...'}
              />
            ))}
          </div>
        </div>

        {/* Control Buttons */}
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-4">
            {/* Play/Pause */}
            <button
              onClick={togglePlayPause}
              className="p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              {isPlaying ? <Pause size={24} /> : <Play size={24} />}
            </button>

            {/* Volume */}
            <div className="flex items-center space-x-2">
              <button onClick={toggleMute} className="p-1 hover:bg-white/20 rounded">
                {isMuted ? <VolumeX size={20} /> : <Volume2 size={20} />}
              </button>
              <input
                type="range"
                min="0"
                max="1"
                step="0.1"
                value={isMuted ? 0 : volume}
                onChange={(e) => handleVolumeChange(parseFloat(e.target.value))}
                className="w-20 h-1 bg-white/20 rounded-lg appearance-none cursor-pointer"
              />
            </div>

            {/* Time Display */}
            <span className="text-sm font-mono">
              {formatTime(currentTime)} / {formatTime(duration)}
            </span>
          </div>

          <div className="flex items-center space-x-4">
            {/* Playback Speed */}
            <select
              value={playbackRate}
              onChange={(e) => changePlaybackRate(parseFloat(e.target.value))}
              className="bg-white/20 text-white rounded px-2 py-1 text-sm"
            >
              <option value={0.5}>0.5x</option>
              <option value={0.75}>0.75x</option>
              <option value={1}>1x</option>
              <option value={1.25}>1.25x</option>
              <option value={1.5}>1.5x</option>
              <option value={2}>2x</option>
            </select>

            {/* Settings */}
            <button className="p-2 hover:bg-white/20 rounded-full transition-colors">
              <Settings size={20} />
            </button>

            {/* Fullscreen */}
            <button
              onClick={toggleFullscreen}
              className="p-2 hover:bg-white/20 rounded-full transition-colors"
            >
              <Maximize size={20} />
            </button>
          </div>
        </div>
      </div>

      {/* Loading Overlay */}
      {!duration && (
        <div className="absolute inset-0 flex items-center justify-center bg-black/50">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white"></div>
        </div>
      )}
    </div>
  );
};
```

### Interactive Transcript Component

```typescript
// src/components/Transcript/InteractiveTranscript.tsx
import React, { useEffect, useRef, useState } from 'react';
import { Search, Highlight, MessageSquare } from 'lucide-react';
import { TranscriptSegment } from '../../types/video';
import { useVideoStore } from '../../stores/videoStore';

interface InteractiveTranscriptProps {
  segments: TranscriptSegment[];
  onSegmentClick: (segment: TranscriptSegment) => void;
  onSegmentHighlight: (segment: TranscriptSegment) => void;
  onAskQuestion: (segment: TranscriptSegment, question: string) => void;
}

export const InteractiveTranscript: React.FC<InteractiveTranscriptProps> = ({
  segments,
  onSegmentClick,
  onSegmentHighlight,
  onAskQuestion
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSegments, setSelectedSegments] = useState<Set<number>>(new Set());
  const [hoveredSegment, setHoveredSegment] = useState<number | null>(null);
  const transcriptRef = useRef<HTMLDivElement>(null);
  
  const { activeSegment, currentTime } = useVideoStore();

  // Auto-scroll to active segment
  useEffect(() => {
    if (activeSegment && transcriptRef.current) {
      const activeElement = transcriptRef.current.querySelector(
        `[data-segment-id="${activeSegment.id}"]`
      );
      if (activeElement) {
        activeElement.scrollIntoView({
          behavior: 'smooth',
          block: 'center'
        });
      }
    }
  }, [activeSegment]);

  // Filter segments based on search
  const filteredSegments = segments.filter(segment =>
    segment.text.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Format time for display
  const formatTime = (time: number): string => {
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  // Handle segment selection for batch operations
  const toggleSegmentSelection = (segmentId: number) => {
    const newSelection = new Set(selectedSegments);
    if (newSelection.has(segmentId)) {
      newSelection.delete(segmentId);
    } else {
      newSelection.add(segmentId);
    }
    setSelectedSegments(newSelection);
  };

  // Handle text selection for contextual questions
  const handleTextSelection = (segment: TranscriptSegment) => {
    const selection = window.getSelection();
    if (selection && selection.toString().trim()) {
      const selectedText = selection.toString();
      const question = `What does "${selectedText}" mean in this context?`;
      onAskQuestion(segment, question);
    }
  };

  return (
    <div className="h-full flex flex-col bg-white rounded-lg shadow-lg">
      {/* Search Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <input
            type="text"
            placeholder="Search transcript..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        
        {selectedSegments.size > 0 && (
          <div className="mt-3 flex items-center space-x-2">
            <span className="text-sm text-gray-600">
              {selectedSegments.size} segment(s) selected
            </span>
            <button
              onClick={() => {
                // Highlight selected segments
                selectedSegments.forEach(id => {
                  const segment = segments.find(s => s.id === id);
                  if (segment) onSegmentHighlight(segment);
                });
                setSelectedSegments(new Set());
              }}
              className="px-3 py-1 bg-yellow-500 text-white rounded text-sm hover:bg-yellow-600 transition-colors"
            >
              <Highlight size={16} className="inline mr-1" />
              Highlight
            </button>
            <button
              onClick={() => setSelectedSegments(new Set())}
              className="px-3 py-1 bg-gray-500 text-white rounded text-sm hover:bg-gray-600 transition-colors"
            >
              Clear
            </button>
          </div>
        )}
      </div>

      {/* Transcript Content */}
      <div 
        ref={transcriptRef}
        className="flex-1 overflow-y-auto p-4 space-y-2"
      >
        {filteredSegments.map((segment) => {
          const isActive = activeSegment?.id === segment.id;
          const isSelected = selectedSegments.has(segment.id);
          const isHovered = hoveredSegment === segment.id;
          
          return (
            <div
              key={segment.id}
              data-segment-id={segment.id}
              className={`
                group relative p-3 rounded-lg cursor-pointer transition-all duration-200
                ${isActive ? 'bg-blue-100 border-l-4 border-blue-500' : ''}
                ${isSelected ? 'bg-yellow-100' : ''}
                ${isHovered && !isActive ? 'bg-gray-50' : ''}
                hover:shadow-md
              `}
              onClick={() => onSegmentClick(segment)}
              onMouseEnter={() => setHoveredSegment(segment.id)}
              onMouseLeave={() => setHoveredSegment(null)}
              onMouseUp={() => handleTextSelection(segment)}
            >
              {/* Timestamp */}
              <div className="flex items-start justify-between mb-2">
                <span className="text-xs font-mono text-gray-500 bg-gray-100 px-2 py-1 rounded">
                  {formatTime(segment.start_time)}
                </span>
                
                {/* Action Buttons */}
                <div className="opacity-0 group-hover:opacity-100 transition-opacity flex space-x-1">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleSegmentSelection(segment.id);
                    }}
                    className={`
                      p-1 rounded text-xs transition-colors
                      ${isSelected ? 'bg-yellow-500 text-white' : 'bg-gray-200 hover:bg-gray-300'}
                    `}
                    title="Select segment"
                  >
                    ✓
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onSegmentHighlight(segment);
                    }}
                    className="p-1 bg-yellow-200 hover:bg-yellow-300 rounded text-xs transition-colors"
                    title="Highlight segment"
                  >
                    <Highlight size={12} />
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      const question = prompt('Ask a question about this segment:');
                      if (question) onAskQuestion(segment, question);
                    }}
                    className="p-1 bg-blue-200 hover:bg-blue-300 rounded text-xs transition-colors"
                    title="Ask question"
                  >
                    <MessageSquare size={12} />
                  </button>
                </div>
              </div>

              {/* Transcript Text */}
              <p className={`
                text-sm leading-relaxed select-text
                ${isActive ? 'font-medium text-blue-900' : 'text-gray-700'}
                ${segment.isHighlighted ? 'bg-yellow-200 px-1 rounded' : ''}
              `}>
                {searchTerm ? (
                  // Highlight search terms
                  segment.text.split(new RegExp(`(${searchTerm})`, 'gi')).map((part, index) =>
                    part.toLowerCase() === searchTerm.toLowerCase() ? (
                      <mark key={index} className="bg-yellow-300 px-1 rounded">
                        {part}
                      </mark>
                    ) : (
                      part
                    )
                  )
                ) : (
                  segment.text
                )}
              </p>

              {/* Confidence Score */}
              {segment.confidence && segment.confidence < 0.8 && (
                <div className="mt-2 text-xs text-orange-600 flex items-center">
                  ⚠️ Low confidence ({Math.round(segment.confidence * 100)}%)
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Footer Stats */}
      <div className="p-3 border-t border-gray-200 text-xs text-gray-500 flex justify-between">
        <span>{filteredSegments.length} segments</span>
        <span>
          {searchTerm && `${filteredSegments.length} of ${segments.length} shown`}
        </span>
      </div>
    </div>
  );
};
```

### State Management with Zustand

```typescript
// src/stores/videoStore.ts
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { Video, TranscriptSegment, AIResponse, UserNote } from '../types/video';

interface VideoState {
  // Current video data
  currentVideo: Video | null;
  transcript: TranscriptSegment[];
  currentTime: number;
  activeSegment: TranscriptSegment | null;
  
  // AI interaction
  aiResponses: AIResponse[];
  isAILoading: boolean;
  
  // Notes
  userNotes: UserNote[];
  
  // UI state
  isTranscriptVisible: boolean;
  isNotesVisible: boolean;
  isAIChatVisible: boolean;
  
  // Actions
  setCurrentVideo: (video: Video) => void;
  setTranscript: (segments: TranscriptSegment[]) => void;
  setCurrentTime: (time: number) => void;
  setActiveSegment: (segment: TranscriptSegment | null) => void;
  addAIResponse: (response: AIResponse) => void;
  setAILoading: (loading: boolean) => void;
  addUserNote: (note: UserNote) => void;
  updateUserNote: (id: number, content: string) => void;
  deleteUserNote: (id: number) => void;
  toggleTranscript: () => void;
  toggleNotes: () => void;
  toggleAIChat: () => void;
  highlightSegment: (segmentId: number) => void;
  clearHighlights: () => void;
}

export const useVideoStore = create<VideoState>()(
  devtools(
    (set, get) => ({
      // Initial state
      currentVideo: null,
      transcript: [],
      currentTime: 0,
      activeSegment: null,
      aiResponses: [],
      isAILoading: false,
      userNotes: [],
      isTranscriptVisible: true,
      isNotesVisible: false,
      isAIChatVisible: false,

      // Actions
      setCurrentVideo: (video) => set({ currentVideo: video }),
      
      setTranscript: (segments) => set({ transcript: segments }),
      
      setCurrentTime: (time) => set({ currentTime: time }),
      
      setActiveSegment: (segment) => set({ activeSegment: segment }),
      
      addAIResponse: (response) => 
        set((state) => ({ 
          aiResponses: [...state.aiResponses, response] 
        })),
      
      setAILoading: (loading) => set({ isAILoading: loading }),
      
      addUserNote: (note) =>
        set((state) => ({
          userNotes: [...state.userNotes, note]
        })),
      
      updateUserNote: (id, content) =>
        set((state) => ({
          userNotes: state.userNotes.map(note =>
            note.id === id ? { ...note, content } : note
          )
        })),
      
      deleteUserNote: (id) =>
        set((state) => ({
          userNotes: state.userNotes.filter(note => note.id !== id)
        })),
      
      toggleTranscript: () =>
        set((state) => ({ isTranscriptVisible: !state.isTranscriptVisible })),
      
      toggleNotes: () =>
        set((state) => ({ isNotesVisible: !state.isNotesVisible })),
      
      toggleAIChat: () =>
        set((state) => ({ isAIChatVisible: !state.isAIChatVisible })),
      
      highlightSegment: (segmentId) =>
        set((state) => ({
          transcript: state.transcript.map(segment =>
            segment.id === segmentId
              ? { ...segment, isHighlighted: true }
              : segment
          )
        })),
      
      clearHighlights: () =>
        set((state) => ({
          transcript: state.transcript.map(segment => ({
            ...segment,
            isHighlighted: false
          }))
        }))
    }),
    { name: 'video-store' }
  )
);
```

This frontend implementation provides:

1. **Custom Video Player** with full controls, keyboard shortcuts, and transcript synchronization
2. **Interactive Transcript** with search, highlighting, and contextual actions
3. **State Management** using Zustand for clean, type-safe state handling
4. **Responsive Design** with Tailwind CSS for modern UI
5. **Accessibility Features** including keyboard navigation and ARIA labels
6. **Performance Optimizations** with React.memo and useCallback where appropriate

The components are designed to work seamlessly together, providing an intuitive and powerful video interaction experience.
