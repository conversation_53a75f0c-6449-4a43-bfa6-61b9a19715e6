# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST
.env
.venv
env/
venv/
ENV/
.pytest_cache/
.coverage
htmlcov/
.ruff_cache/
.mypy_cache/

# Node/React
node_modules/
frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*
.npm
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.env.local
.env.development.local
.env.test.local
.env.production.local
frontend/dist/
frontend/build/
frontend/coverage/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
*.swn
*.bak
*.sublime*
.settings/
.project
.classpath
*.launch
.history/

# OS
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.lnk

# Project specific
logs/
*.log
.env*
!.env.example
celerybeat-schedule
celerybeat.pid
*.sqlite3
*.db

# Database
*.sql
*.sqlite
*.db
*.db-journal

# Testing and coverage
.tox/
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Build and distribution
dist/
build/
*.min.js
*.min.css

# Temporary files
*.tmp
*.temp
*.bak
*.swp
*~

# Dependencies lock files (keep these in version control)
!package-lock.json
!yarn.lock
!poetry.lock
!requirements.txt
!dev-requirements.txt

# Local development configurations
.localconfig/
local_settings.py

.qodo

*.lock
.trunk