# Core React and Framework
react
react-dom
typescript
vite

# Routing
react-router-dom

# State Management
zustand
@tanstack/react-query
@tanstack/react-query-devtools

# UI Components and Styling
@headlessui/react
@heroicons/react
tailwindcss
@tailwindcss/vite
clsx
class-variance-authority

# Speech and Voice Features (Free)
react-speech-recognition
react-speakup

# Form Handling
react-hook-form
@hookform/resolvers
zod

# HTTP Client
axios
ky

# Date and Time
date-fns
dayjs

# Utilities
lodash
ramda
uuid

# Icons
lucide-react
react-icons

# Animation
framer-motion
@react-spring/web
lottie-react

# Charts and Visualization
recharts
d3
@visx/visx
react-chartjs-2
chart.js

# Rich Text Editor
@tiptap/react
@tiptap/starter-kit
@tiptap/extension-link
@tiptap/extension-image
@tiptap/extension-youtube

# File Upload
react-dropzone
react-file-drop

# Notifications and Toasts
react-hot-toast
react-toastify
sonner

# Modal and Dialog
@radix-ui/react-dialog
@radix-ui/react-dropdown-menu
@radix-ui/react-select
@radix-ui/react-tooltip
@radix-ui/react-popover

# Video Player
react-player
video.js
@videojs/themes

# Audio Visualization
wavesurfer.js
react-wavesurfer

# Markdown
react-markdown
remark-gfm
rehype-highlight
rehype-raw

# Code Highlighting
prismjs
react-syntax-highlighter

# Virtual Scrolling
react-window
react-virtualized-auto-sizer

# Drag and Drop
@dnd-kit/core
@dnd-kit/sortable
@dnd-kit/utilities
react-beautiful-dnd

# Search and Filtering
fuse.js
match-sorter

# Internationalization
react-i18next
i18next
i18next-browser-languagedetector

# Development Tools
@types/react
@types/react-dom
@types/node
@types/lodash
@types/uuid

# Build Tools
@vitejs/plugin-react
@vitejs/plugin-react-swc
vite-plugin-pwa
vite-plugin-windicss

# Testing
@testing-library/react
@testing-library/jest-dom
@testing-library/user-event
vitest
jsdom
happy-dom

# Linting and Formatting
eslint
@typescript-eslint/eslint-plugin
@typescript-eslint/parser
eslint-plugin-react
eslint-plugin-react-hooks
eslint-plugin-react-refresh
prettier
prettier-plugin-tailwindcss

# PWA
workbox-window
workbox-precaching

# Performance
@loadable/component
react-lazy-load-image-component

# Accessibility
@axe-core/react
react-focus-lock
focus-trap-react

# Error Handling
react-error-boundary

# Development Utilities
@storybook/react
@storybook/addon-essentials
@storybook/addon-interactions
@storybook/testing-library

# Optional: Advanced Features
react-helmet-async
react-intersection-observer
react-use
react-hotkeys-hook
use-debounce
use-local-storage-state

# Optional: Advanced UI
@mui/material
@mui/icons-material
@emotion/react
@emotion/styled
antd
chakra-ui

# Optional: Advanced Animation
react-transition-group
react-reveal
aos

# Optional: Advanced Charts
plotly.js
react-plotly.js
@nivo/core
@nivo/line
@nivo/bar

# Optional: Advanced Forms
formik
react-final-form
react-jsonschema-form

# Optional: Advanced Tables
@tanstack/react-table
react-data-grid
ag-grid-react

# Optional: Advanced Date Pickers
react-datepicker
@mui/x-date-pickers

# Optional: Advanced File Handling
react-pdf
react-csv
xlsx

# Optional: Advanced Maps
react-leaflet
leaflet
@react-google-maps/api

# Optional: Advanced Media
react-webcam
react-image-crop
react-image-gallery

# Optional: Advanced Utils
immer
reselect
normalizr

# Optional: Advanced Routing
wouter
reach-router

# Optional: Advanced State
redux
@reduxjs/toolkit
react-redux
recoil
jotai

# Optional: Advanced HTTP
swr
react-query
apollo-client
@apollo/client

# Optional: Advanced Validation
yup
joi
superstruct

# Optional: Advanced Testing
cypress
playwright
@playwright/test
puppeteer

# Optional: Advanced Build
rollup
webpack
parcel

# Optional: Advanced Styling
styled-components
emotion
stitches
vanilla-extract

# Optional: Advanced Performance
react-window-infinite-loader
react-virtualized
@tanstack/react-virtual

# Optional: Advanced Development
react-devtools
why-did-you-render
@welldone-software/why-did-you-render

# Optional: Advanced Security
dompurify
xss

# Optional: Advanced Utilities
polished
color2k
tinycolor2

# Optional: Advanced Accessibility
@reach/router
@reach/dialog
@reach/menu-button

# Optional: Advanced Gestures
react-use-gesture
@use-gesture/react

# Optional: Advanced Audio
tone
web-audio-api
audio-context

# Optional: Advanced Video
hls.js
dash.js
shaka-player

# Optional: Advanced WebRTC
simple-peer
socket.io-client

# Optional: Advanced Workers
comlink
workerize-loader

# Optional: Advanced Storage
localforage
idb
dexie

# Optional: Advanced Crypto
crypto-js
bcryptjs
jsencrypt

# Optional: Advanced Compression
pako
lz-string
fflate

# Optional: Advanced Parsing
papaparse
xml2js
yaml

# Optional: Advanced Math
mathjs
ml-matrix
simple-statistics

# Optional: Advanced Algorithms
fuzzysort
string-similarity
levenshtein

# Optional: Advanced Networking
axios-retry
ky-universal
cross-fetch
