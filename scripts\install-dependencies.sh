#!/bin/bash

# InsightStream Dependency Installation Script
# This script installs all dependencies with their latest versions

set -e  # Exit on any error

echo "🚀 InsightStream Dependency Installation"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if uv is installed
check_uv() {
    if ! command -v uv &> /dev/null; then
        print_error "uv is not installed. Please install it first:"
        echo "curl -LsSf https://astral.sh/uv/install.sh | sh"
        exit 1
    fi
    print_success "uv is installed: $(uv --version)"
}

# Check if Node.js is installed
check_node() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 18+ first."
        exit 1
    fi
    print_success "Node.js is installed: $(node --version)"
}

# Check if npm is installed
check_npm() {
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed. Please install npm first."
        exit 1
    fi
    print_success "npm is installed: $(npm --version)"
}

# Install backend dependencies
install_backend() {
    print_status "Installing backend dependencies..."

    cd backend

    # Initialize uv project if not already done
    if [ ! -f "pyproject.toml" ]; then
        print_status "Initializing uv project..."
        uv init --python 3.13
    fi

    # Install production dependencies
    print_status "Installing production dependencies from requirements.txt..."
    uv add -r requirements.txt

    # Install development dependencies
    print_status "Installing development dependencies from dev-requirements.txt..."
    uv add --dev -r dev-requirements.txt

    cd ..
    print_success "Backend dependencies installed successfully!"
}

# Install frontend dependencies
install_frontend() {
    print_status "Installing frontend dependencies..."

    cd frontend

    # Install base dependencies
    if [ -f "package.json" ]; then
        print_status "Installing existing package.json dependencies..."
        npm install
    else
        print_status "Initializing new React project..."
        npm create vite@latest . -- --template react-ts
        npm install
    fi

    # Install core packages
    print_status "Installing core React packages..."
    npm install react@latest react-dom@latest typescript@latest

    # Install routing
    print_status "Installing routing packages..."
    npm install react-router-dom@latest

    # Install state management
    print_status "Installing state management packages..."
    npm install zustand@latest @tanstack/react-query@latest @tanstack/react-query-devtools@latest

    # Install Tailwind CSS v4 (2025 latest)
    print_status "Installing Tailwind CSS v4 with Vite plugin..."
    npm install tailwindcss@latest @tailwindcss/vite@latest

    # Install UI and styling
    print_status "Installing UI and styling packages..."
    npm install @headlessui/react@latest @heroicons/react@latest clsx@latest

    # Install speech features (free)
    print_status "Installing speech recognition packages..."
    npm install react-speech-recognition@latest react-speakup@latest

    # Install form handling
    print_status "Installing form handling packages..."
    npm install react-hook-form@latest @hookform/resolvers@latest zod@latest

    # Install HTTP client
    print_status "Installing HTTP client packages..."
    npm install axios@latest ky@latest

    # Install utilities
    print_status "Installing utility packages..."
    npm install date-fns@latest lodash@latest uuid@latest

    # Install animation
    print_status "Installing animation packages..."
    npm install framer-motion@latest

    # Install notifications
    print_status "Installing notification packages..."
    npm install react-hot-toast@latest sonner@latest

    # Install video player
    print_status "Installing video player packages..."
    npm install react-player@latest

    # Install development dependencies
    print_status "Installing development dependencies..."
    npm install --save-dev @types/react@latest @types/react-dom@latest @types/node@latest @types/lodash@latest @types/uuid@latest
    npm install --save-dev @vitejs/plugin-react@latest vite@latest
    npm install --save-dev eslint@latest @typescript-eslint/eslint-plugin@latest @typescript-eslint/parser@latest
    npm install --save-dev prettier@latest prettier-plugin-tailwindcss@latest
    npm install --save-dev @testing-library/react@latest @testing-library/jest-dom@latest vitest@latest jsdom@latest

    cd ..
    print_success "Frontend dependencies installed successfully!"
}

# Update package versions
update_packages() {
    print_status "Updating all packages to latest versions..."

    # Update backend packages
    print_status "Updating backend packages..."
    cd backend
    uv sync --upgrade
    cd ..

    # Update frontend packages
    print_status "Updating frontend packages..."
    cd frontend
    npm update
    cd ..

    print_success "All packages updated to latest versions!"
}

# Generate requirements files with current versions
generate_lockfiles() {
    print_status "Generating lock files with current versions..."

    # Generate backend lock file
    cd backend
    uv export --format requirements-txt > requirements.lock
    uv export --format requirements-txt --dev > dev-requirements.lock
    cd ..

    # Frontend already generates package-lock.json automatically

    print_success "Lock files generated successfully!"
}

# Main installation function
main() {
    print_status "Starting InsightStream dependency installation..."

    # Check prerequisites
    check_uv
    check_node
    check_npm

    # Parse command line arguments
    INSTALL_BACKEND=true
    INSTALL_FRONTEND=true
    UPDATE_PACKAGES=false
    GENERATE_LOCKS=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            --backend-only)
                INSTALL_FRONTEND=false
                shift
                ;;
            --frontend-only)
                INSTALL_BACKEND=false
                shift
                ;;
            --update)
                UPDATE_PACKAGES=true
                shift
                ;;
            --generate-locks)
                GENERATE_LOCKS=true
                shift
                ;;
            --help)
                echo "Usage: $0 [OPTIONS]"
                echo "Options:"
                echo "  --backend-only     Install only backend dependencies"
                echo "  --frontend-only    Install only frontend dependencies"
                echo "  --update          Update all packages to latest versions"
                echo "  --generate-locks  Generate lock files with current versions"
                echo "  --help            Show this help message"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done

    # Install dependencies
    if [ "$INSTALL_BACKEND" = true ]; then
        install_backend
    fi

    if [ "$INSTALL_FRONTEND" = true ]; then
        install_frontend
    fi

    # Update packages if requested
    if [ "$UPDATE_PACKAGES" = true ]; then
        update_packages
    fi

    # Generate lock files if requested
    if [ "$GENERATE_LOCKS" = true ]; then
        generate_lockfiles
    fi

    print_success "🎉 InsightStream dependency installation completed successfully!"
    echo ""
    echo "Next steps:"
    echo "1. Copy .env.example to .env and configure your environment variables"
    echo "2. Set up your Neon PostgreSQL database"
    echo "3. Get your API keys (Groq, Agno, etc.)"
    echo "4. Run 'make dev' to start the development environment"
    echo ""
    echo "For more information, see the documentation in docs/"
}

# Run main function
main "$@"
