"""
Quick setup test to verify FastAPI application works correctly.

This script tests the basic functionality of the InsightStream backend
without requiring a full database setup.
"""

import asyncio
import os
import sys
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent
sys.path.insert(0, str(backend_dir))

# Set environment variables for testing
os.environ.setdefault("ENVIRONMENT", "testing")
os.environ.setdefault("DATABASE_URL", "sqlite+aiosqlite:///./test.db")
os.environ.setdefault("SECRET_KEY", "test-secret-key")
os.environ.setdefault("LOG_LEVEL", "INFO")


async def test_basic_setup():
    """Test basic application setup."""
    print("🧪 Testing InsightStream Backend Setup...")
    
    try:
        # Test configuration loading
        print("📋 Testing configuration...")
        from app.core.config import get_settings
        
        settings = get_settings()
        assert settings.APP_NAME == "InsightStream"
        assert settings.VERSION == "0.1.0"
        print("✅ Configuration loaded successfully")
        
        # Test logging setup
        print("📝 Testing logging...")
        from app.core.logging import setup_logging, get_logger
        
        setup_logging()
        logger = get_logger("test")
        logger.info("Test log message")
        print("✅ Logging configured successfully")
        
        # Test database configuration (without actual connection)
        print("🗄️ Testing database configuration...")
        from app.core.database import get_engine
        
        # This will create the engine but not connect
        engine = get_engine()
        assert engine is not None
        print("✅ Database engine configured successfully")
        
        # Test FastAPI app creation
        print("🚀 Testing FastAPI application...")
        from app.main import app
        
        assert app.title == "InsightStream API"
        assert app.version == "0.1.0"
        print("✅ FastAPI application created successfully")
        
        # Test exception classes
        print("⚠️ Testing exception handling...")
        from app.core.exceptions import (
            InsightStreamException,
            ValidationException,
            AuthenticationException,
        )
        
        # Test custom exception
        try:
            raise ValidationException("Test validation error", field="test_field")
        except ValidationException as e:
            assert e.error_code == "VALIDATION_ERROR"
            assert e.status_code == 422
        
        print("✅ Exception handling working correctly")
        
        print("\n🎉 All tests passed! InsightStream Backend setup is working correctly.")
        print("\n📋 Next steps:")
        print("1. Set up your database (PostgreSQL recommended)")
        print("2. Copy .env.example to .env and configure your settings")
        print("3. Install dependencies: uv sync")
        print("4. Run the application: uv run uvicorn app.main:app --reload")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_with_testclient():
    """Test with FastAPI TestClient."""
    try:
        print("\n🔧 Testing with FastAPI TestClient...")
        
        from fastapi.testclient import TestClient
        from app.main import app
        
        # Override database dependency for testing
        def override_get_db():
            # Return a mock database session for testing
            return None
        
        # Create test client
        client = TestClient(app)
        
        # Test root endpoint
        response = client.get("/")
        assert response.status_code == 200
        data = response.json()
        assert data["message"] == "Welcome to InsightStream API"
        assert data["version"] == "0.1.0"
        
        print("✅ Root endpoint working")
        
        # Test health endpoint
        response = client.get("/health")
        assert response.status_code == 200
        data = response.json()
        assert data["status"] == "healthy"
        
        print("✅ Health endpoint working")
        
        print("✅ TestClient tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ TestClient test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all setup tests."""
    print("🚀 InsightStream Backend Setup Test")
    print("=" * 50)
    
    # Run basic setup test
    success = asyncio.run(test_basic_setup())
    
    if success:
        # Run TestClient test
        success = asyncio.run(test_with_testclient())
    
    if success:
        print("\n🎯 All tests completed successfully!")
        print("Your InsightStream backend is ready for development!")
        sys.exit(0)
    else:
        print("\n💥 Some tests failed. Please check the errors above.")
        sys.exit(1)


if __name__ == "__main__":
    main()
