# InsightStream Backend Environment Configuration
# Copy this file to .env and update with your actual values

# Application Settings
APP_NAME=InsightStream
ENVIRONMENT=development
DEBUG=true
SECRET_KEY=your-super-secret-key-change-this-in-production

# Server Settings
HOST=0.0.0.0
PORT=8000
WORKERS=1

# CORS and Security
CORS_ORIGINS=http://localhost:3000,http://localhost:3001
ALLOWED_HOSTS=localhost,127.0.0.1

# Database Configuration (PostgreSQL with asyncpg)
# For local development, you can use a local PostgreSQL instance
# For production, use a cloud provider like Neon, Supabase, or AWS RDS
DATABASE_URL=postgresql+asyncpg://insightstream_user:your_password@localhost:5432/insightstream_db
DATABASE_ECHO=false
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20

# Redis Configuration
# For local development, use local Redis
# For production, use Redis Cloud or AWS ElastiCache
REDIS_URL=redis://localhost:6379/0

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/1

# AI Service API Keys
# Get these from respective providers
AGNO_API_KEY=your-agno-api-key-here
OPENAI_API_KEY=your-openai-api-key-here
GROQ_API_KEY=your-groq-api-key-here
ELEVENLABS_API_KEY=your-elevenlabs-api-key-here

# Logging and Monitoring
# Get Logfire token from https://logfire.pydantic.dev/
LOGFIRE_TOKEN=your-logfire-token-here
SENTRY_DSN=your-sentry-dsn-here
LOG_LEVEL=INFO

# File Storage (AWS S3 or compatible)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
S3_BUCKET_NAME=insightstream-storage

# Video Processing Settings
MAX_VIDEO_DURATION=7200
MAX_FILE_SIZE=524288000
SUPPORTED_VIDEO_FORMATS=mp4,avi,mov,mkv,webm

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_PER_HOUR=1000

# Email Settings (optional)
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=InsightStream

# Vector Database (Qdrant)
QDRANT_URL=http://localhost:6333
QDRANT_API_KEY=your-qdrant-api-key

# Performance Settings
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT=30
VECTOR_DIMENSION=384
